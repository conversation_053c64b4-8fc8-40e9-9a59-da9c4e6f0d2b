# -*- coding: utf-8 -*-
"""
MLP模型的工具函数
"""

import torch
import numpy as np
from models.gat.utils import create_data_masks
from models.utils.data_loader import load_and_preprocess_test_data


def train_mlp_model(
    localization, hidden_channels=128, dropout=0.1, lr=0.001, weight_decay=0.0005
):
    """
    训练MLP模型，支持超参数传递，返回验证集损失、平均误差和最佳模型参数

    参数:
        localization: RFIDLocalization实例
        hidden_channels: 隐藏层神经元数量
        dropout: Dropout比率
        lr: 学习率
        weight_decay: 权重衰减

    返回:
        best_val_loss: 最佳验证损失
        best_val_avg_distance: 最佳验证集平均误差
        best_model: 最佳模型参数
    """
    # 创建训练、验证和测试掩码
    train_mask, val_mask, test_mask = create_data_masks(
        len(localization.features_norm), localization.config, localization.device
    )
    train_mask = train_mask | test_mask

    X = localization.features_norm.to(localization.device)
    y = localization.labels_norm.to(localization.device)

    # 使用原始训练数据
    X_train_tensor = X[train_mask]
    y_train_tensor = y[train_mask]

    # 创建MLP模型
    torch.manual_seed(localization.config['RANDOM_SEED'])
    if torch.cuda.is_available():
        torch.cuda.manual_seed(localization.config['RANDOM_SEED'])
        torch.cuda.manual_seed_all(localization.config['RANDOM_SEED'])
    from models.mlp.model import MLPLocalizationModel
    localization.mlp_model = MLPLocalizationModel(
        in_channels=X.shape[1],
        hidden_channels=hidden_channels,
        out_channels=2,
        dropout=dropout
    ).to(localization.device)

    data_min = torch.as_tensor(
        localization.labels_scaler.data_min_, dtype=torch.float32
    ).to(localization.device)
    data_range = torch.as_tensor(
        localization.labels_scaler.data_range_, dtype=torch.float32
    ).to(localization.device)

    torch.manual_seed(localization.config['RANDOM_SEED'])
    if torch.cuda.is_available():
        torch.cuda.manual_seed(localization.config['RANDOM_SEED'])
        torch.cuda.manual_seed_all(localization.config['RANDOM_SEED'])
    optimizer = torch.optim.Adam(
        localization.mlp_model.parameters(), lr=lr, weight_decay=weight_decay
    )
    loss_fn = torch.nn.MSELoss()

    best_val_loss = float('inf')
    best_model = None
    patience = localization.config.get('PATIENCE', 50)
    counter = 0
    best_val_avg_distance = float('inf')

    localization.mlp_train_losses = []
    localization.mlp_val_losses = []

    for epoch in range(localization.config.get('EPOCHS', 1000)):
        localization.mlp_model.train()
        optimizer.zero_grad()
        out = localization.mlp_model(X_train_tensor)
        train_loss = loss_fn(out, y_train_tensor)
        train_loss.backward()
        optimizer.step()

        # 验证阶段
        localization.mlp_model.eval()
        with torch.no_grad():
            val_out = localization.mlp_model(X[val_mask])
            val_loss = loss_fn(val_out, y[val_mask])
            out_orig = val_out * data_range + data_min
            y_orig = y[val_mask] * data_range + data_min
            val_distances = torch.sqrt(
                torch.sum((out_orig - y_orig)**2, dim=1))
            val_accuracy = (val_distances < 0.3).float().mean().item() * 100
            val_avg_distance = val_distances.mean().item()

        # 保存每个epoch的损失值
        localization.mlp_train_losses.append(train_loss.item())
        localization.mlp_val_losses.append(val_loss.item())

        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_val_avg_distance = val_avg_distance
            best_model = localization.mlp_model.state_dict().copy()
            counter = 0
        else:
            counter += 1
            if counter >= patience:
                if localization.config['TRAIN_LOG']:
                    log_message = (
                        f"MLP轮次 {epoch}\n"
                        f"验证集 - 损失: {val_loss.item():.4f}, 准确率: {val_accuracy:.2f}%, 平均误差: {val_avg_distance:.2f}米\n"
                        f"\n触发早停！在轮次 {epoch} 停止训练\n"
                        f"最佳验证损失: {best_val_loss:.4f}"
                    )
                    localization.train_logger.info(log_message)
                localization.mlp_model.load_state_dict(best_model)
                break

        if epoch % 100 == 0 and localization.config['TRAIN_LOG']:
            log_message = (
                f"MLP轮次 {epoch}\n"
                f"训练集 - 损失: {train_loss.item():.4f}\n"
                f"验证集 - 损失: {val_loss.item():.4f}, 准确率: {val_accuracy:.2f}%, 平均误差: {val_avg_distance:.2f}米"
            )

            localization.train_logger.info(log_message)

    # 训练结束后加载最佳模型
    localization.mlp_model.load_state_dict(best_model)
    return best_val_loss, best_val_avg_distance, best_model


def evaluate_mlp_on_new_data(localization, test_features=None, test_labels=None):
    """
    评估MLP模型在新数据上的性能，只使用RSSI特征

    参数:
        localization: RFIDLocalization实例
        test_features: 测试特征，如果为None则从文件加载
        test_labels: 测试标签，如果为None则从文件加载

    返回:
        predictions_orig: 预测位置
        test_labels_np: 真实位置
        avg_distance: 平均预测误差
        distances: 所有样本的误差列表
    """
    if localization.mlp_model is None:
        raise ValueError("MLP模型未训练。请先调用train_mlp_model。")

    if test_features is None or test_labels is None:
        test_features, test_labels, test_features_np, test_labels_np, _ = load_and_preprocess_test_data(
            localization.config['TEST_DATA_PATH']
        )
    else:
        if isinstance(test_features, torch.Tensor):
            test_features_np = test_features.cpu().numpy()
        else:
            test_features_np = test_features

        if isinstance(test_labels, torch.Tensor):
            test_labels_np = test_labels.cpu().numpy()
        else:
            test_labels_np = test_labels

    rssi_values = test_features_np[:, :4]

    rssi_norm = localization.scaler_rssi.transform(rssi_values)

    features_norm = rssi_norm
    features_tensor = torch.tensor(features_norm,
                                   dtype=torch.float32).to(localization.device)

    localization.mlp_model.eval()
    with torch.no_grad():
        predictions = localization.mlp_model(features_tensor)

        predictions_orig = localization.labels_scaler.inverse_transform(
            predictions.cpu().numpy()
        )

        # 计算欧几里得距离误差
        distances = np.sqrt(
            np.sum((test_labels_np - predictions_orig)**2, axis=1))
        avg_distance = np.mean(distances)

    if localization.config['PREDICTION_LOG']:
        log_message = "\nMLP模型新标签位置预测评估:\n"
        log_message += f"测试样本数量: {len(test_features)}\n"
        log_message += f"平均预测误差: {avg_distance:.2f}米\n"
        log_message += f"最大误差: {np.max(distances):.2f}米\n"
        log_message += f"最小误差: {np.min(distances):.2f}米\n"
        log_message += f"误差标准差: {np.std(distances):.2f}米\n"

        for threshold in [0.5, 1.0, 1.5, 2.0]:
            accuracy = np.mean(distances < threshold) * 100
            log_message += f"误差 < {threshold}米的准确率: {accuracy:.2f}%\n"

        localization.prediction_logger.info(log_message)

    return predictions_orig, test_labels_np, avg_distance, distances
