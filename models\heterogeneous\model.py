# -*- coding: utf-8 -*-
"""
异构图神经网络模型用于RFID定位
"""

import torch
import torch.nn.functional as F
import time
from torch_geometric.nn import GATv2Conv, HeteroConv, Linear


class HeterogeneousGNNModel(torch.nn.Module):
    """
    异构图神经网络模型

    使用异构图注意力网络处理不同类型的节点和边
    """

    def __init__(self, in_channels, hidden_channels, out_channels, heads=3):
        """
        初始化异构图神经网络模型

        参数:
            in_channels: 输入特征维度
            hidden_channels: 隐藏层维度
            out_channels: 输出维度
            heads: 注意力头数量
        """
        super(HeterogeneousGNNModel, self).__init__()

        self.perf_stats = {
            'forward_calls': 0,
            'data_to_device': 0,
            'node_transform': 0,
            'conv1': 0,
            'conv2': 0,
            'conv3': 0,
            'mlp_head': 0,
            'total_forward_time': 0,  # 添加总前向传播时间
            'batch_times': []  # 记录每批次的时间
        }

        # 设置随机种子以确保初始化的一致性
        try:
            from config import CONFIG
            random_seed = CONFIG['RANDOM_SEED']
            torch.manual_seed(random_seed)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(random_seed)
                torch.cuda.manual_seed_all(random_seed)
            # 增加以下设置以确保完全确定性
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
        except:
            # 如果无法导入配置，使用默认种子
            torch.manual_seed(42)
            if torch.cuda.is_available():
                torch.cuda.manual_seed(42)
                torch.cuda.manual_seed_all(42)
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False

        # 定义节点类型和关系类型
        self.node_types = ['tag', 'antenna', 'chair']
        self.edge_types = [
            ('tag', 'to', 'tag'),  # 标签到标签的关系
            ('tag', 'to', 'antenna'),  # 标签到天线的关系
            ('antenna', 'to', 'tag'),  # 天线到标签的关系
            ('tag', 'to', 'chair'),  # 标签到椅子的关系
            ('chair', 'to', 'tag'),  # 椅子到标签的关系
            ('chair', 'to', 'antenna'),  # 椅子到天线的关系
            ('antenna', 'to', 'chair'),  # 天线到椅子的关系
            ('chair', 'obstructs', 'tag')  # 椅子遮挡标签的关系
        ]

        # 特征分离处理：RSSI和相位特征分别处理
        self.rssi_dim = 4
        self.phase_dim = 4 if in_channels > 4 else 0

        # 2.2 优化：添加信号差异特征处理
        self.use_signal_diff_features = True
        # 信号差异特征转换层 - 处理天线间RSSI差异
        self.signal_diff_transform = torch.nn.Sequential(
            torch.nn.Linear(self.rssi_dim * (self.rssi_dim -
                            1) // 2, hidden_channels // 2),
            torch.nn.ReLU(),
            torch.nn.LayerNorm(hidden_channels // 2),
        )

        # 融合原始特征和差异特征
        self.feature_fusion = torch.nn.Sequential(
            torch.nn.Linear(hidden_channels + hidden_channels //
                            2, hidden_channels),
            torch.nn.ReLU(),
            torch.nn.LayerNorm(hidden_channels),
        )

        # 优化：使用torch.nn.ModuleList以减少字典查找开销
        self.node_transforms = torch.nn.ModuleList([
            torch.nn.Sequential(
                Linear(in_channels, hidden_channels),
                torch.nn.ReLU(),
                torch.nn.LayerNorm(hidden_channels),
            ) for _ in range(3)  # tag, antenna, chair
        ])

        # 添加简单的信号可靠性评估网络 - 自适应注意力机制
        self.reliability_net = torch.nn.ModuleDict({
            'tag': torch.nn.Sequential(
                torch.nn.Linear(hidden_channels, 16),
                torch.nn.ReLU(),
                torch.nn.Linear(16, 1),
                torch.nn.Sigmoid()  # 输出0-1之间的可靠性分数
            )
        })

        self.node_type_to_idx = {'tag': 0, 'antenna': 1, 'chair': 2}

        # 1.4 优化：注意力正则化参数
        self.attention_l2_reg = 0.01  # 注意力权重L2正则化强度
        self.attention_entropy_reg = 0.01  # 注意力熵正则化强度
        self.edge_drop_rate = 0.05  # 边随机丢弃率，用于注意力正则化

        # 1.6 优化：多尺度注意力和跨类型注意力机制
        self.use_multiscale_attention = False  # 暂时禁用多尺度注意力
        self.multiscale_alpha = 0.3  # 多尺度特征融合权重

        self.attention_stats = {
            'multiscale_used': 0,
            'cross_attention_used': 0,
            'multiscale_improvement': [],  # 存储多尺度特征前后的误差改进
            'cross_attention_improvement': []  # 存储跨类型注意力前后的误差改进
        }

        # 跨类型注意力网络 - 用于增强不同节点类型之间的交互
        self.cross_attention = torch.nn.ModuleDict({
            'tag_to_antenna': torch.nn.Sequential(
                torch.nn.Linear(hidden_channels, hidden_channels // 2),
                torch.nn.ReLU(),
                torch.nn.Linear(hidden_channels // 2, 1),
                torch.nn.Sigmoid()
            ),
            'antenna_to_tag': torch.nn.Sequential(
                torch.nn.Linear(hidden_channels, hidden_channels // 2),
                torch.nn.ReLU(),
                torch.nn.Linear(hidden_channels // 2, 1),
                torch.nn.Sigmoid()
            ),
            'tag_to_chair': torch.nn.Sequential(
                torch.nn.Linear(hidden_channels, hidden_channels // 2),
                torch.nn.ReLU(),
                torch.nn.Linear(hidden_channels // 2, 1),
                torch.nn.Sigmoid()
            ),
            'chair_to_tag': torch.nn.Sequential(
                torch.nn.Linear(hidden_channels, hidden_channels // 2),
                torch.nn.ReLU(),
                torch.nn.Linear(hidden_channels // 2, 1),
                torch.nn.Sigmoid()
            )
        })

        # 多尺度特征提取模块 - 为每种节点类型提取不同尺度的特征
        # 修改：针对每个头数的特征维度进行处理
        self.multiscale_modules = torch.nn.ModuleDict({
            'tag': torch.nn.ModuleList([
                torch.nn.Linear(hidden_channels, hidden_channels),
                torch.nn.Linear(hidden_channels, hidden_channels),
                torch.nn.Linear(hidden_channels, hidden_channels)
            ]),
            'antenna': torch.nn.ModuleList([
                torch.nn.Linear(hidden_channels, hidden_channels),
                torch.nn.Linear(hidden_channels, hidden_channels)
            ]),
            'chair': torch.nn.ModuleList([
                torch.nn.Linear(hidden_channels, hidden_channels),
                torch.nn.Linear(hidden_channels, hidden_channels)
            ])
        })

        self.multiscale_fusion = torch.nn.ModuleDict({
            'tag': torch.nn.Linear(hidden_channels * 3, hidden_channels),
            'antenna': torch.nn.Linear(hidden_channels * 2, hidden_channels),
            'chair': torch.nn.Linear(hidden_channels * 2, hidden_channels)
        })

        # 头数适配层 - 用于多尺度特征与多头注意力特征之间的适配
        self.head_adapters = torch.nn.ModuleDict({
            'tag_conv1': torch.nn.Linear(hidden_channels, hidden_channels * heads),
            'tag_conv2': torch.nn.Linear(hidden_channels, hidden_channels * heads),
            # 第三层只有1个头
            'tag_conv3': torch.nn.Linear(hidden_channels, hidden_channels)
        })

        self.conv1 = HeteroConv({
            ('tag', 'to', 'tag'):
                GATv2Conv(
                    hidden_channels,
                    hidden_channels,
                    heads=heads,
                    add_self_loops=True,
                    edge_dim=1
            ),
            ('tag', 'to', 'antenna'):
                GATv2Conv(
                    hidden_channels,
                    hidden_channels,
                    heads=heads,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('antenna', 'to', 'tag'):
                GATv2Conv(
                    hidden_channels,
                    hidden_channels,
                    heads=heads,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('tag', 'to', 'chair'):
                GATv2Conv(
                    hidden_channels,
                    hidden_channels,
                    heads=heads,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('chair', 'to', 'tag'):
                GATv2Conv(
                    hidden_channels,
                    hidden_channels,
                    heads=heads,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('chair', 'to', 'antenna'):
                GATv2Conv(
                    hidden_channels,
                    hidden_channels,
                    heads=heads,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('antenna', 'to', 'chair'):
                GATv2Conv(
                    hidden_channels,
                    hidden_channels,
                    heads=heads,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('chair', 'obstructs', 'tag'):
                GATv2Conv(
                    hidden_channels,
                    hidden_channels,
                    heads=heads,
                    add_self_loops=False,
                    edge_dim=1
            ),
        },
            aggr='sum')

        self.conv2 = HeteroConv({
            ('tag', 'to', 'tag'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=heads,
                    dropout=0.1,
                    add_self_loops=True,
                    edge_dim=1
            ),
            ('tag', 'to', 'antenna'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=heads,
                    dropout=0.05,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('antenna', 'to', 'tag'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=heads,
                    dropout=0.05,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('tag', 'to', 'chair'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=heads,
                    dropout=0.1,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('chair', 'to', 'tag'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=heads,
                    dropout=0.1,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('chair', 'to', 'antenna'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=heads,
                    dropout=0.1,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('antenna', 'to', 'chair'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=heads,
                    dropout=0.1,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('chair', 'obstructs', 'tag'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=heads,
                    dropout=0.05,
                    add_self_loops=False,
                    edge_dim=1
            ),
        },
            aggr='sum')

        self.conv3 = HeteroConv({
            ('tag', 'to', 'tag'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=1,  # 设为1个头以保持维度一致
                    dropout=0.1,
                    add_self_loops=True,
                    edge_dim=1
            ),
            ('tag', 'to', 'antenna'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=1,  # 设为1个头以保持维度一致
                    dropout=0.05,  # 降低dropout
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('antenna', 'to', 'tag'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=1,  # 设为1个头以保持维度一致
                    dropout=0.05,  # 降低dropout
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('tag', 'to', 'chair'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=1,  # 设为1个头以保持维度一致
                    dropout=0.1,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('chair', 'to', 'tag'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=1,  # 设为1个头以保持维度一致
                    dropout=0.1,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('chair', 'to', 'antenna'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=1,  # 设为1个头以保持维度一致
                    dropout=0.1,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('antenna', 'to', 'chair'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=1,  # 设为1个头以保持维度一致
                    dropout=0.1,
                    add_self_loops=False,
                    edge_dim=1
            ),
            ('chair', 'obstructs', 'tag'):
                GATv2Conv(
                    hidden_channels * heads,
                    hidden_channels,
                    heads=1,  # 设为1个头以保持维度一致
                    dropout=0.05,  # 降低dropout
                    add_self_loops=False,
                    edge_dim=1
            ),
        },
            aggr='sum')

        # 优化：使用融合操作的MLP替代序列操作
        self.fc = torch.nn.Sequential(
            torch.nn.Linear(hidden_channels * 3, hidden_channels * 2),
            torch.nn.ReLU(inplace=True),
            torch.nn.LayerNorm(hidden_channels * 2),
            torch.nn.Dropout(0.15),
            torch.nn.Linear(hidden_channels * 2, hidden_channels),
            torch.nn.ReLU(inplace=True),
            torch.nn.LayerNorm(hidden_channels),
            torch.nn.Dropout(0.1),
            torch.nn.Linear(hidden_channels, out_channels)
        )

        self.res_fc1 = Linear(hidden_channels, hidden_channels)
        self.res_fc2 = Linear(hidden_channels * heads, hidden_channels)

        self.res_conv1_tag = Linear(hidden_channels * heads, hidden_channels)
        self.res_conv2_tag = Linear(hidden_channels * heads, hidden_channels)
        self.res_conv3_tag = Linear(
            hidden_channels, hidden_channels)  # 第三层只有1个头

        self.cross_res1_to_3 = Linear(hidden_channels, hidden_channels)
        self.cross_res2_to_3 = Linear(hidden_channels, hidden_channels)

        self.dense_in_to_conv2 = Linear(
            hidden_channels, hidden_channels * heads)
        self.dense_in_to_conv3 = Linear(hidden_channels, hidden_channels)

        self.dim_adapter_2_to_3 = Linear(
            hidden_channels * heads, hidden_channels)

        self.skip_in_to_out = Linear(hidden_channels, out_channels)
        self.skip_conv1_to_out = Linear(hidden_channels * heads, out_channels)
        self.skip_alpha = 0.15  # 跳跃连接权重，减小以降低对正常输出的干扰
        self.skip_beta = 0.1   # 第二个跳跃连接权重

        self.fusion_layer = torch.nn.Sequential(
            Linear(hidden_channels * 5, hidden_channels * 2),
            torch.nn.LayerNorm(hidden_channels * 2),
            torch.nn.ReLU(inplace=True),
            torch.nn.Dropout(0.1),
            Linear(hidden_channels * 2, hidden_channels)
        )

        self.register_buffer('empty_tensor', torch.tensor([]))

        self.use_cuda_streams = hasattr(
            torch.cuda, 'Stream') and torch.cuda.is_available()

        if self.use_cuda_streams:
            try:
                self.transform_stream = torch.cuda.Stream()
                self.main_stream = torch.cuda.default_stream()
            except:
                self.use_cuda_streams = False

        self.heads = heads

        self.layer_norms = torch.nn.ModuleDict({
            'tag_1': torch.nn.LayerNorm(hidden_channels * heads),
            'tag_2': torch.nn.LayerNorm(hidden_channels * heads),
            'tag_3': torch.nn.LayerNorm(hidden_channels),  # 第三层只有1个头
        })

        self.use_layer_norm = True

        # 为每层的tag节点添加门控更新机制
        self.gates = torch.nn.ModuleDict({
            'update_gate_1': torch.nn.Sequential(
                Linear(hidden_channels * heads * 2, hidden_channels * heads),
                torch.nn.Dropout(0.1),
                torch.nn.Sigmoid()
            ),
            'reset_gate_1': torch.nn.Sequential(
                Linear(hidden_channels * heads * 2, hidden_channels * heads),
                torch.nn.Dropout(0.1),
                torch.nn.Sigmoid()
            ),
            'candidate_1': torch.nn.Sequential(
                Linear(hidden_channels * heads * 2, hidden_channels * heads),
                torch.nn.Dropout(0.1)
            ),

            'update_gate_2': torch.nn.Sequential(
                Linear(hidden_channels * heads * 2, hidden_channels * heads),
                torch.nn.Dropout(0.1),
                torch.nn.Sigmoid()
            ),
            'reset_gate_2': torch.nn.Sequential(
                Linear(hidden_channels * heads * 2, hidden_channels * heads),
                torch.nn.Dropout(0.1),
                torch.nn.Sigmoid()
            ),
            'candidate_2': torch.nn.Sequential(
                Linear(hidden_channels * heads * 2, hidden_channels * heads),
                torch.nn.Dropout(0.1)
            ),

            'update_gate_3': torch.nn.Sequential(
                Linear(hidden_channels * 2, hidden_channels),
                torch.nn.Dropout(0.1),
                torch.nn.Sigmoid()
            ),
            'reset_gate_3': torch.nn.Sequential(
                Linear(hidden_channels * 2, hidden_channels),
                torch.nn.Dropout(0.1),
                torch.nn.Sigmoid()
            ),
            'candidate_3': torch.nn.Sequential(
                Linear(hidden_channels * 2, hidden_channels),
                torch.nn.Dropout(0.1)
            )
        })

        for name, module in self.gates.items():
            if 'update_gate' in name:
                for m in module:
                    if isinstance(m, Linear):
                        torch.nn.init.constant_(m.bias, -1.0)
            elif 'reset_gate' in name:
                for m in module:
                    if isinstance(m, Linear):
                        torch.nn.init.constant_(m.bias, 1.0)

        # 添加特征转换适配器，用于门控机制中维度调整
        self.feature_adapters = torch.nn.ModuleDict({
            'layer1': Linear(hidden_channels, hidden_channels * heads),
            'layer2_3': Linear(hidden_channels * heads, hidden_channels)
        })

        self.use_gating = True
        self.gating_dropout = 0.1

        self.gating_stats = {
            'layer1_update_mean': [],
            'layer2_update_mean': [],
            'layer3_update_mean': []
        }

    def forward(self, x_dict, edge_index_dict, edge_attr_dict):
        """
        前向传播

        参数:
            x_dict: 节点特征字典 {node_type: features}
            edge_index_dict: 边索引字典 {edge_type: edge_indices}
            edge_attr_dict: 边属性字典 {edge_type: edge_attributes}

        返回:
            tag节点的位置预测
        """
        self.perf_stats['forward_calls'] += 1
        forward_start_time = time.time()

        device = next(self.parameters()).device

        disable_timing = True

        if not disable_timing:
            start_time = time.time()

        if not disable_timing:
            start_time = time.time()

        x_dict_moved = {}
        for k, v in x_dict.items():
            if v.device != device:
                x_dict_moved[k] = v.to(device, non_blocking=True)
            else:
                x_dict_moved[k] = v

        for k, v in edge_index_dict.items():
            if v.device != device:
                edge_index_dict[k] = v.to(device, non_blocking=True)

        for k, v in edge_attr_dict.items():
            if v.device != device:
                edge_attr_dict[k] = v.to(device, non_blocking=True)

        if not disable_timing:
            torch.cuda.synchronize(device) if device.type == 'cuda' else None
            self.perf_stats['data_to_device'] += time.time() - start_time

        x_dict_transformed = {}

        if self.use_cuda_streams and device.type == 'cuda':
            with torch.cuda.stream(self.transform_stream):
                for node_type, x in x_dict_moved.items():
                    if node_type in self.node_type_to_idx:
                        idx = self.node_type_to_idx[node_type]
                        x_dict_transformed[node_type] = self.node_transforms[idx](
                            x)
                    else:
                        x_dict_transformed[node_type] = x

            torch.cuda.current_stream().wait_stream(self.transform_stream)
        else:
            if not disable_timing:
                start_time = time.time()

            for node_type, x in x_dict_moved.items():
                if node_type in self.node_type_to_idx:
                    idx = self.node_type_to_idx[node_type]
                    x_dict_transformed[node_type] = self.node_transforms[idx](
                        x)
                else:
                    x_dict_transformed[node_type] = x

            if not disable_timing:
                torch.cuda.synchronize(
                    device) if device.type == 'cuda' else None
                self.perf_stats['node_transform'] += time.time() - start_time

        if self.use_signal_diff_features and 'tag' in x_dict_transformed:
            tag_features = x_dict_transformed['tag']
            batch_size = tag_features.shape[0]

            rssi_features = tag_features[:, :self.rssi_dim]

            diff_features = []
            for i in range(self.rssi_dim):
                for j in range(i+1, self.rssi_dim):
                    diff = rssi_features[:, i:i+1] - rssi_features[:, j:j+1]
                    diff_features.append(diff)

            diff_features = torch.cat(diff_features, dim=1)

            transformed_diff = self.signal_diff_transform(diff_features)

            original_transformed = x_dict_transformed['tag']
            fused_features = torch.cat(
                [original_transformed, transformed_diff], dim=1)
            x_dict_transformed['tag'] = self.feature_fusion(fused_features)

        # 1.6 优化：添加多尺度特征提取
        # 暂时禁用多尺度注意力，直接跳过这一部分
        multiscale_features = {}  # 保留空字典以避免后续代码引用错误
        if False and self.use_multiscale_attention and self.training:  # 添加False条件确保不执行
            for node_type, features in x_dict_transformed.items():
                if node_type in self.multiscale_modules:
                    # 记录使用多尺度注意力的次数
                    self.attention_stats['multiscale_used'] += 1

                    # 应用不同尺度的线性变换
                    multiscale_outputs = []
                    for linear in self.multiscale_modules[node_type]:
                        ms_output = linear(features)
                        ms_output = F.relu(ms_output)
                        multiscale_outputs.append(ms_output)

                    concatenated = torch.cat(multiscale_outputs, dim=1)

                    fused_features = self.multiscale_fusion[node_type](
                        concatenated)
                    fused_features = F.relu(fused_features)

                    x_dict_transformed[node_type] = (
                        1 - self.multiscale_alpha) * features + self.multiscale_alpha * fused_features

                    multiscale_features[node_type] = fused_features

        reliability_score = None
        if 'tag' in x_dict_transformed and 'tag' in self.reliability_net:
            reliability_score = self.reliability_net['tag'](
                x_dict_transformed['tag'])

        if False and self.training and 'tag' in x_dict_transformed and 'antenna' in x_dict_transformed:
            self.attention_stats['cross_attention_used'] += 1

            tag_features = x_dict_transformed['tag']
            antenna_features = x_dict_transformed['antenna']

            sample_size = min(antenna_features.size(0), 8)
            if antenna_features.size(0) > sample_size:
                indices = torch.randperm(antenna_features.size(0), device=device)[
                    :sample_size]
                sampled_antenna_features = antenna_features[indices]
            else:
                sampled_antenna_features = antenna_features

            cross_attention_weights = []
            for antenna_feat in sampled_antenna_features:
                expanded_antenna = antenna_feat.expand(
                    tag_features.size(0), -1)
                interaction = tag_features * expanded_antenna
                attention_weight = self.cross_attention['tag_to_antenna'](
                    interaction)
                cross_attention_weights.append(attention_weight)

            if cross_attention_weights:
                cross_attention = torch.stack(cross_attention_weights, dim=1)
                cross_attention = torch.mean(
                    cross_attention, dim=1)

                enhanced_tag_features = tag_features * \
                    (1.0 + 0.2 * cross_attention)
                x_dict_transformed['tag'] = enhanced_tag_features

            if 'antenna_to_tag' in self.cross_attention:
                sample_size = min(tag_features.size(0), 16)
                if tag_features.size(0) > sample_size:
                    indices = torch.randperm(tag_features.size(0), device=device)[
                        :sample_size]
                    sampled_tag_features = tag_features[indices]
                else:
                    sampled_tag_features = tag_features

                cross_attention_weights = []
                for tag_feat in sampled_tag_features:
                    expanded_tag = tag_feat.expand(
                        antenna_features.size(0), -1)
                    interaction = antenna_features * expanded_tag
                    attention_weight = self.cross_attention['antenna_to_tag'](
                        interaction)
                    cross_attention_weights.append(attention_weight)

                if cross_attention_weights:
                    cross_attention = torch.stack(
                        cross_attention_weights, dim=1)
                    cross_attention = torch.mean(cross_attention, dim=1)
                    enhanced_antenna_features = antenna_features * \
                        (1.0 + 0.2 * cross_attention)
                    x_dict_transformed['antenna'] = enhanced_antenna_features

        tag_input = x_dict_transformed.get('tag', None)

        edge_index_dict_reg = {}
        edge_attr_dict_reg = {}
        for edge_type, edge_index in edge_index_dict.items():
            if self.training and torch.rand(1).item() < self.edge_drop_rate:
                num_edges = edge_index.size(1)
                perm = torch.randperm(num_edges, device=edge_index.device)
                keep_num = int(num_edges * (1 - self.edge_drop_rate))
                keep_indices = perm[:keep_num]

                edge_index_dict_reg[edge_type] = edge_index[:, keep_indices]
                if edge_type in edge_attr_dict:
                    edge_attr_dict_reg[edge_type] = edge_attr_dict[edge_type][keep_indices]
            else:
                edge_index_dict_reg[edge_type] = edge_index
                if edge_type in edge_attr_dict:
                    edge_attr_dict_reg[edge_type] = edge_attr_dict[edge_type]

        if not disable_timing:
            start_time = time.time()

        x_dict1 = self.conv1(x_dict_transformed,
                             edge_index_dict_reg, edge_attr_dict_reg)

        attention_l2_loss = 0
        attention_entropy_loss = 0

        self.attention_weights = {}

        if reliability_score is not None and 'tag' in x_dict1:
            x = x_dict1['tag']
            rel_score = reliability_score

            if x.dim() > 2:
                rel_factor = rel_score.view(-1, 1, 1)
            else:
                rel_factor = rel_score.view(-1, 1)
            # 扩展维度以匹配特征形状
            if x.dim() > 2:  # 如果有多个头
                rel_factor = rel_score.view(-1, 1, 1)  # [N, 1, 1]
            else:
                rel_factor = rel_score.view(-1, 1)  # [N, 1]

            # 使用较小的调整范围，减少过拟合风险
            scaling = 0.85 + 0.15 * rel_factor  # 基础值高，调整幅度小
            x_dict1['tag'] = x * scaling

        # 1.7 优化：应用层归一化 - 只在tag节点上应用
        if self.use_layer_norm and 'tag' in x_dict1:
            norm_key = 'tag_1'
            x_dict1['tag'] = self.layer_norms[norm_key](x_dict1['tag'])

        # 1.8 优化：应用门控更新机制 - 第一层
        if self.use_gating and 'tag' in x_dict1 and 'tag' in x_dict_transformed:
            # 准备输入和输出特征
            prev_h = x_dict_transformed['tag']  # 原始特征
            curr_h = x_dict1['tag']  # 卷积后的特征

            # 将输入特征调整为与卷积输出相同的维度
            prev_h_adapted = self.feature_adapters['layer1'](prev_h)

            # 拼接特征
            combined = torch.cat([prev_h_adapted, curr_h], dim=1)

            # 更新门 - 决定多少比例的旧信息被保留
            update_gate = self.gates['update_gate_1'](combined)

            # 收集统计信息
            if self.training:
                self.gating_stats['layer1_update_mean'].append(
                    update_gate.mean().item())

            # 重置门 - 决定多少比例的旧信息用于候选隐藏状态
            reset_gate = self.gates['reset_gate_1'](combined)

            # 计算候选隐藏状态
            reset_combined = torch.cat(
                [reset_gate * prev_h_adapted, curr_h], dim=1)
            candidate = torch.tanh(self.gates['candidate_1'](reset_combined))

            # 最终输出 - 结合旧特征和新特征
            # 调整更新门的影响，缓解过拟合
            update_gate = update_gate * 0.8 + 0.1  # 限制在0.1-0.9之间，避免极端值
            x_dict1['tag'] = (1 - update_gate) * \
                prev_h_adapted + update_gate * candidate

        # 直接在字典上应用操作，减少中间结果
        for k, v in x_dict1.items():
            x_dict1[k] = F.dropout(F.relu(v), p=0.1, training=self.training)

        if not disable_timing:
            torch.cuda.synchronize(device) if device.type == 'cuda' else None
            self.perf_stats['conv1'] += time.time() - start_time

        # 保存第一层输出用于多级残差连接和跳跃连接
        tag_conv1 = x_dict1.get('tag', None)

        # 1.5 优化：内部残差连接 - 第一层
        if 'tag' in x_dict1:
            # 应用残差变换
            tag_res1 = self.res_conv1_tag(x_dict1['tag'])
            # 保存用于跨层连接
            tag_res1_for_cross = tag_res1.clone()

        # 第二层异构卷积
        if not disable_timing:
            start_time = time.time()

        # 1.6 优化：结合多尺度特征和残差连接
        if 'tag' in x_dict1 and tag_input is not None:
            # 将原始输入变换到第二层输入的维度
            tag_dense_to_conv2 = self.dense_in_to_conv2(tag_input)

            # 只使用原始连接，禁用多尺度特征
            x_dict1['tag'] = x_dict1['tag'] + 0.2 * tag_dense_to_conv2

        x_dict2 = self.conv2(x_dict1, edge_index_dict_reg, edge_attr_dict_reg)

        # 应用自适应注意力：根据标签节点可靠性调整特征 (第二层)
        if reliability_score is not None and 'tag' in x_dict2:
            x = x_dict2['tag']
            rel_score = reliability_score

            # 扩展维度以匹配特征形状
            if x.dim() > 2:  # 如果有多个头
                rel_factor = rel_score.view(-1, 1, 1)  # [N, 1, 1]
            else:
                rel_factor = rel_score.view(-1, 1)  # [N, 1]

            # 第二层使用稍大的调整幅度
            scaling = 0.8 + 0.2 * rel_factor
            x_dict2['tag'] = x * scaling

        # 1.7 优化：应用层归一化 - 只在tag节点上应用
        if self.use_layer_norm and 'tag' in x_dict2:
            norm_key = 'tag_2'
            x_dict2['tag'] = self.layer_norms[norm_key](x_dict2['tag'])

        # 1.8 优化：应用门控更新机制 - 第二层
        if self.use_gating and 'tag' in x_dict2 and 'tag' in x_dict1:
            # 准备输入和输出特征
            prev_h = x_dict1['tag']  # 前一层的特征
            curr_h = x_dict2['tag']  # 当前层的特征

            # 这里不需要维度调整，因为第一层和第二层的tag特征维度相同

            # 拼接特征
            combined = torch.cat([prev_h, curr_h], dim=1)

            # 更新门
            update_gate = self.gates['update_gate_2'](combined)

            # 收集统计信息
            if self.training:
                self.gating_stats['layer2_update_mean'].append(
                    update_gate.mean().item())

            # 重置门
            reset_gate = self.gates['reset_gate_2'](combined)

            # 候选隐藏状态
            reset_combined = torch.cat([reset_gate * prev_h, curr_h], dim=1)
            candidate = torch.tanh(self.gates['candidate_2'](reset_combined))

            # 最终输出
            # 调整更新门的影响，缓解过拟合
            update_gate = update_gate * 0.8 + 0.1  # 限制在0.1-0.9之间，避免极端值
            x_dict2['tag'] = (1 - update_gate) * prev_h + \
                update_gate * candidate

        # 直接在字典上应用操作
        for k, v in x_dict2.items():
            x_dict2[k] = F.dropout(F.relu(v), p=0.1, training=self.training)

        if not disable_timing:
            torch.cuda.synchronize(device) if device.type == 'cuda' else None
            self.perf_stats['conv2'] += time.time() - start_time

        # 保存第二层输出用于多级残差连接和跳跃连接
        tag_conv2 = x_dict2.get('tag', None)

        # 1.5 优化：内部残差连接 - 第二层
        if 'tag' in x_dict2:
            # 应用残差变换
            tag_res2 = self.res_conv2_tag(x_dict2['tag'])
            # 保存用于跨层连接
            tag_res2_for_cross = tag_res2.clone()

        # 第三层异构卷积
        if not disable_timing:
            start_time = time.time()

        # 1.5 优化：将第二层输出、第一层输出和原始输入混合后传递给第三层
        if 'tag' in x_dict2 and tag_input is not None:
            # 不要改变x_dict2['tag']的维度，保持原始维度用于第三层卷积
            x_dict2_tag_orig = x_dict2['tag'].clone()

            # 将输入和残差特征调整为与x_dict2['tag']相同的维度
            # 密集残差 - 从输入到第三层 (调整为与第二层输出相同的维度)
            tag_dense_to_conv3_expanded = self.dense_in_to_conv3(tag_input)
            # 扩展维度以匹配第二层输出
            tag_dense_to_conv3 = tag_dense_to_conv3_expanded.repeat(
                1, self.heads)

            # 跨层残差 - 从第一层到第三层 (调整为与第二层输出相同的维度)
            if tag_conv1 is not None:
                # 将第一层残差特征调整为与第二层输出相同的维度
                tag_cross1_to_3 = self.cross_res1_to_3(tag_res1_for_cross)
                tag_cross1_to_3_expanded = tag_cross1_to_3.repeat(
                    1, self.heads)

                # 混合特征 - 保持原始维度
                x_dict2['tag'] = x_dict2_tag_orig + 0.15 * \
                    tag_dense_to_conv3 + 0.3 * tag_cross1_to_3_expanded
            else:
                # 如果没有第一层输出，只混合输入
                x_dict2['tag'] = x_dict2_tag_orig + 0.15 * tag_dense_to_conv3

        x_dict3 = self.conv3(x_dict2, edge_index_dict_reg, edge_attr_dict_reg)

        # 应用自适应注意力：根据标签节点可靠性调整特征 (第三层)
        if reliability_score is not None and 'tag' in x_dict3:
            x = x_dict3['tag']
            rel_score = reliability_score

            # 扩展维度以匹配特征形状（第三层只有1个头）
            rel_factor = rel_score.view(-1, 1)  # [N, 1]

            # 第三层使用最大的调整幅度
            scaling = 0.75 + 0.25 * rel_factor
            x_dict3['tag'] = x * scaling

        # 1.7 优化：应用层归一化 - 只在tag节点上应用，并在激活函数前应用
        if self.use_layer_norm and 'tag' in x_dict3:
            norm_key = 'tag_3'
            x_dict3['tag'] = self.layer_norms[norm_key](x_dict3['tag'])

        # 1.8 优化：应用门控更新机制 - 第三层
        if self.use_gating and 'tag' in x_dict3 and 'tag' in x_dict2:
            # 准备输入和输出特征
            prev_h = x_dict2['tag']  # 前一层的特征
            curr_h = x_dict3['tag']  # 当前层的特征

            # 由于第二层和第三层维度不同，需要调整
            prev_h_adapted = self.feature_adapters['layer2_3'](prev_h)

            # 拼接特征
            combined = torch.cat([prev_h_adapted, curr_h], dim=1)

            # 更新门
            update_gate = self.gates['update_gate_3'](combined)

            # 收集统计信息
            if self.training:
                self.gating_stats['layer3_update_mean'].append(
                    update_gate.mean().item())

            # 重置门
            reset_gate = self.gates['reset_gate_3'](combined)

            # 候选隐藏状态
            reset_combined = torch.cat(
                [reset_gate * prev_h_adapted, curr_h], dim=1)
            candidate = torch.tanh(self.gates['candidate_3'](reset_combined))

            # 最终输出
            # 调整更新门的影响，缓解过拟合
            update_gate = update_gate * 0.8 + 0.1  # 限制在0.1-0.9之间，避免极端值
            x_dict3['tag'] = (1 - update_gate) * \
                prev_h_adapted + update_gate * candidate

        # 直接应用ReLU，减少中间结果
        for k, v in x_dict3.items():
            x_dict3[k] = F.relu(v)

        if not disable_timing:
            torch.cuda.synchronize(device) if device.type == 'cuda' else None
            self.perf_stats['conv3'] += time.time() - start_time

        # 1.5 优化：使用密集残差连接和跨层连接，增强特征表示
        if not disable_timing:
            start_time = time.time()

        if 'tag' in x_dict3 and tag_input is not None:
            # 1. 原始输入变换
            tag_input_transformed = self.res_fc1(tag_input)

            # 2. 第一层输出变换（跨层连接）
            tag_conv1_transformed = None
            if tag_conv1 is not None:
                tag_conv1_transformed = self.cross_res1_to_3(
                    tag_res1_for_cross)
            else:
                # 如果没有第一层输出，使用变换后的输入
                tag_conv1_transformed = tag_input_transformed

            # 3. 第二层输出变换
            tag_conv2_transformed = None
            if tag_conv2 is not None:
                tag_conv2_transformed = self.cross_res2_to_3(
                    tag_res2_for_cross)
            else:
                # 如果没有第二层输出，使用变换后的输入
                tag_conv2_transformed = tag_input_transformed

            # 4. 第三层的内部残差
            tag_conv3_transformed = self.res_conv3_tag(x_dict3['tag'])

            # 5. 拼接所有变换后的特征
            tag_concat = torch.cat([
                x_dict3['tag'],              # 当前层特征
                tag_input_transformed,       # 原始输入
                tag_conv1_transformed,       # 第一层特征
                tag_conv2_transformed,       # 第二层特征
                tag_conv3_transformed        # 当前层的残差变换
            ], dim=1)

            # 6. 使用融合层整合所有特征
            fused_features = self.fusion_layer(tag_concat)

            # 7. 检查全连接层的输入维度
            fc_input_dim = self.fc[0].in_features

            # 如果维度不匹配，动态调整
            if fused_features.shape[1] != fc_input_dim:
                # 创建临时适配层
                adapter = torch.nn.Linear(
                    fused_features.shape[1], fc_input_dim).to(fused_features.device)
                # 初始化为恒等映射
                torch.nn.init.eye_(adapter.weight[:, :min(
                    fused_features.shape[1], fc_input_dim)])
                if adapter.bias is not None:
                    torch.nn.init.zeros_(adapter.bias)
                # 应用适配层
                fused_features = adapter(fused_features)

            # 1.6 优化：应用跳跃连接 - 直接从输入层和第一层连接到输出
            # 正常输出计算
            regular_out = self.fc(fused_features)

            # 简化跳跃连接 - 使用加权和而不是全连接层
            if tag_input is not None and tag_conv1 is not None:
                # 输入层到输出层的跳跃连接
                tag_skip_in_to_out = self.skip_in_to_out(tag_input)

                # 第一层到输出层的跳跃连接
                tag_skip_conv1_to_out = self.skip_conv1_to_out(tag_conv1)

                # 加权和融合 - 避免全连接层可能引起的过拟合
                out = regular_out + self.skip_alpha * tag_skip_in_to_out + \
                    self.skip_beta * tag_skip_conv1_to_out
            else:
                # 如果跳跃连接不可用，使用常规输出
                out = regular_out
        else:
            # 如果没有标签节点，返回预分配的空张量
            out = self.empty_tensor

        if not disable_timing:
            torch.cuda.synchronize(device) if device.type == 'cuda' else None
            self.perf_stats['mlp_head'] += time.time() - start_time

        # 记录总前向传播时间
        forward_time = time.time() - forward_start_time
        self.perf_stats['total_forward_time'] += forward_time
        self.perf_stats['batch_times'].append(forward_time)

        # 1.4 添加注意力正则化损失，作为模型的额外属性
        # 在训练脚本中可以访问这些属性并添加到损失中
        self.attention_l2_loss = attention_l2_loss
        self.attention_entropy_loss = attention_entropy_loss

        return out

    def get_performance_stats(self):
        """获取模型性能统计信息"""
        # 深度复制以避免返回引用
        stats = self.perf_stats.copy()

        # 计算平均值
        forward_calls = max(1, stats.get('forward_calls', 1))  # 避免除零错误
        stats['data_to_device_avg'] = stats.get(
            'data_to_device', 0) / forward_calls
        stats['node_transform_avg'] = stats.get(
            'node_transform', 0) / forward_calls
        stats['conv1_avg'] = stats.get('conv1', 0) / forward_calls
        stats['conv2_avg'] = stats.get('conv2', 0) / forward_calls
        stats['conv3_avg'] = stats.get('conv3', 0) / forward_calls
        stats['mlp_head_avg'] = stats.get('mlp_head', 0) / forward_calls
        stats['forward_time_avg'] = stats.get(
            'total_forward_time', 0) / forward_calls

        # 计算最近100次平均值
        if len(self.perf_stats['batch_times']) > 0:
            stats['recent_avg'] = sum(
                self.perf_stats['batch_times'][-100:]) / min(100, len(self.perf_stats['batch_times']))
        else:
            stats['recent_avg'] = 0

        # 1.6 优化：添加注意力机制统计
        if hasattr(self, 'attention_stats'):
            # 添加多尺度注意力和跨类型注意力的使用统计
            stats['multiscale_used'] = self.attention_stats.get(
                'multiscale_used', 0)
            stats['cross_attention_used'] = self.attention_stats.get(
                'cross_attention_used', 0)

            # 计算平均改进率
            ms_improvements = self.attention_stats.get(
                'multiscale_improvement', [])
            if ms_improvements:
                stats['multiscale_avg_improvement'] = sum(
                    ms_improvements) / len(ms_improvements)
            else:
                stats['multiscale_avg_improvement'] = 0

            ca_improvements = self.attention_stats.get(
                'cross_attention_improvement', [])
            if ca_improvements:
                stats['cross_attention_avg_improvement'] = sum(
                    ca_improvements) / len(ca_improvements)
            else:
                stats['cross_attention_avg_improvement'] = 0

        # 1.8 优化：添加门控更新统计
        if hasattr(self, 'gating_stats'):
            # 添加各层门控更新值的平均值
            layer1_means = self.gating_stats.get('layer1_update_mean', [])
            if layer1_means:
                stats['layer1_gate_mean'] = sum(
                    layer1_means) / len(layer1_means)
            else:
                stats['layer1_gate_mean'] = 0

            layer2_means = self.gating_stats.get('layer2_update_mean', [])
            if layer2_means:
                stats['layer2_gate_mean'] = sum(
                    layer2_means) / len(layer2_means)
            else:
                stats['layer2_gate_mean'] = 0

            layer3_means = self.gating_stats.get('layer3_update_mean', [])
            if layer3_means:
                stats['layer3_gate_mean'] = sum(
                    layer3_means) / len(layer3_means)
            else:
                stats['layer3_gate_mean'] = 0

        return stats

    def reset_attention_stats(self):
        """重置注意力统计信息"""
        if hasattr(self, 'attention_stats'):
            self.attention_stats = {
                'multiscale_used': 0,
                'cross_attention_used': 0,
                'multiscale_improvement': [],
                'cross_attention_improvement': []
            }

    def reset_gating_stats(self):
        """重置门控更新统计信息"""
        if hasattr(self, 'gating_stats'):
            self.gating_stats = {
                'layer1_update_mean': [],
                'layer2_update_mean': [],
                'layer3_update_mean': []
            }

    # 启用JIT编译来加速模型
    def compile(self):
        """
        使用torch.compile加速模型（仅适用于PyTorch 2.0及以上版本）
        """
        if hasattr(torch, 'compile'):
            try:
                return torch.compile(self, mode='reduce-overhead')
            except Exception as e:
                print(f"模型编译失败: {e}")
                print("继续使用未编译的模型")
        return self
