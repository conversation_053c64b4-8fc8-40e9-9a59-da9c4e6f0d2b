---
description:
globs:
alwaysApply: false
---
# 异构图神经网络模型

## 核心文件

- [models/heterogeneous/model.py](mdc:models/heterogeneous/model.py) - 异构图模型定义
- [models/heterogeneous/utils.py](mdc:models/heterogeneous/utils.py) - 异构图工具函数
- [models/heterogeneous/train.py](mdc:models/heterogeneous/train.py) - 异构图模型训练函数

## 模型架构

异构图模型考虑了多种节点类型和边类型:

### 节点类型
- `tag`: RFID标签节点
- `antenna`: 天线节点
- `obstacle`: 障碍物节点(如椅子、桌子等)

### 边类型
- `tag__to__tag`: 标签与标签之间的连接
- `tag__to__antenna`: 标签与天线之间的连接
- `tag__to__obstacle`: 标签与障碍物之间的连接

### 边特征
- 标签间边特征: 距离、RSSI差异等
- 标签-天线边特征: 距离、信号强度等
- 标签-障碍物边特征: 距离、材质影响等

## 关键函数

- `create_heterogeneous_graph_data`: 创建异构图数据
- `add_new_node_to_hetero_graph`: 向异构图添加新节点
- `train_hetero_model`: 训练异构图模型
- `evaluate_prediction_hetero_accuracy`: 评估异构图模型性能
- `record_hetero_prediction_time`: 记录异构图模型预测时间

## 性能优化

异构图模型包含多种性能优化技术:

1. 边特征加权: 使用`EDGE_ATTR_WEIGHTS`配置不同类型边的权重
2. 注意力机制: 使用多头注意力提高模型表现
3. 集成学习: 与MLP模型集成，通过`HETERO_ENSEMBLE_WEIGHT`控制权重
4. 可选的PyTorch编译加速: 通过`USE_TORCH_COMPILE`配置启用

## 使用方法

在[test.py](mdc:test.py)中，可以通过以下方式使用异构图模型:

```python
# 初始化RFID定位系统
rfid_system = RFIDLocalization()
rfid_system.load_data()

# 训练异构图模型
rfid_system.train_hetero_model(
    hidden_channels=64,
    heads=2,
    lr=0.005,
    weight_decay=1e-5
)

# 评估异构图模型
mae, rmse, max_error = rfid_system.evaluate_prediction_hetero_accuracy()
```

