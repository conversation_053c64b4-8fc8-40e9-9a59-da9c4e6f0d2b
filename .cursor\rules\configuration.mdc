---
description:
globs:
alwaysApply: true
---
# RFID-GATv2 配置指南

[config.py](mdc:config.py) 文件包含项目的所有配置参数，通过CONFIG字典进行管理。

## 基本配置

- `K`：KNN算法的K值，默认为7
- `RANDOM_SEED`：随机种子，确保实验可重现，设为42
- `TRAIN_LOG`：是否启用训练日志记录
- `PREDICTION_LOG`：是否启用预测日志记录
- `GRID_SEARCH`：是否启用网格搜索寻找最优超参数
- `QUICK_SEARCH`：是否使用快速搜索（减少组合数量）

## 算法开关

- `OPEN_KNN`：是否启用KNN算法
- `OPEN_MLP`：是否启用MLP算法
- `OPEN_GAT`：是否启用GAT算法
- `OPEN_HETERO`：是否启用异构图算法
- `OPEN_LANDMARC`：是否启用LANDMARC算法

## 环境设置

- `ANTENNA_LOCATIONS`：四个天线的位置坐标
- `CHAIR_INFO`：环境中障碍物（椅子、桌子等）的信息
- `EDGE_ATTR_WEIGHTS`：边属性权重配置

## 模型参数

- `MODEL_PARAMS`：各模型的默认参数
  - GAT参数：K、lr、weight_decay、hidden_channels、heads
  - MLP参数：lr、weight_decay、hidden_channels、dropout
  - 异构图参数：lr、weight_decay、hidden_channels、heads

## 训练参数

- `EPOCHS`：最大训练轮次，默认1000
- `PATIENCE`：早停耐心值，默认100

## 网格搜索参数

- `GRID_SEARCH_PARAMS`：网格搜索的参数范围
  - k_range：K值范围
  - lr_range：学习率范围
  - weight_decay_range：权重衰减范围
  - hidden_channels_range：隐藏层维度范围
  - heads_range：注意力头数范围
  - dropout_range：丢弃率范围

## 结果保存

- `RESULTS_DIR`：结果保存目录
- `MODEL_COMPARISON_IMAGE`：模型对比图片保存路径

