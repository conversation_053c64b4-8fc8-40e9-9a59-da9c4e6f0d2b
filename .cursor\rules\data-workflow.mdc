---
description:
globs:
alwaysApply: true
---
# 数据处理工作流

## 数据文件

- 参考标签数据: [data/rfid_reference_tags.csv](mdc:data/rfid_reference_tags.csv)
- 测试标签数据: [data/rfid_test_tags.csv](mdc:data/rfid_test_tags.csv)

## 数据格式

RFID数据包含以下主要字段:
- `rssi_antenna1~4`: 标签的RSSI值
- `wrapped_phase_antenna1~4`: 标签的没有解缠的相位值
- `true_x`, `true_y`: 标签真实坐标

## 数据加载与预处理

数据加载和预处理主要在[RFIDLocalization](mdc:test.py)类的`load_data`方法中实现:

1. 加载CSV文件数据
2. 提取特征(RSSI和相位)和标签(位置坐标)
3. 对特征进行标准化处理
4. 构建图结构(对于图神经网络模型)

## 图结构构建

对于图神经网络模型，需要构建图结构:

1. 使用KNN算法构建邻接矩阵
2. 将邻接矩阵转换为PyTorch Geometric格式的图数据
3. 添加边特征(如节点间距离、RSSI差异等)

## 异构图构建

异构图模型需要特殊的图构建过程，在[models/heterogeneous/utils.py](mdc:models/heterogeneous/utils.py)中实现:

1. 创建多种类型的节点(标签节点、天线节点、障碍物节点)
2. 定义多种类型的边关系
3. 为不同类型的边添加特定的边特征

## 数据仿真
1. data下的数据是通过 [仿真代码.py](mdc:仿真代码.py) 代码生成
4. 不要使用[仿真代码.py](mdc:仿真代码.py)代码进行数据增强



































































