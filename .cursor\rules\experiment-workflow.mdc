---
description:
globs:
alwaysApply: false
---
# 实验工作流

## 实验入口

项目的主要实验入口是[test.py](mdc:test.py)文件中的`main`函数。

## 实验流程

1. 初始化`RFIDLocalization`类
2. 加载和预处理数据
3. 根据配置选择要运行的算法
4. 执行网格搜索(如果启用)
5. 训练模型
6. 评估模型性能
7. 可视化结果

## 网格搜索

如果在配置中启用了网格搜索(`GRID_SEARCH=True`)，系统会:

1. 遍历指定的超参数组合
2. 对每组参数训练和评估模型
3. 记录每组参数的性能指标
4. 保存最佳参数组合

网格搜索的实现在[models/utils/grid_search.py](mdc:models/utils/grid_search.py)中。

## 模型训练

各模型的训练函数:

- GAT模型: `train_gat_model`
- MLP模型: `train_mlp_model`
- 异构图模型: `train_hetero_model`

训练过程包括:
1. 初始化模型
2. 设置优化器和损失函数
3. 循环训练指定轮数
4. 使用早停策略避免过拟合
5. 记录训练和验证损失

## 模型评估

各模型的评估函数:

- GAT模型: `evaluate_prediction_GAT_accuracy`
- MLP模型: `evaluate_mlp_on_new_data`
- LANDMARC模型: `evaluate_landmarc`
- 异构图模型: `evaluate_prediction_hetero_accuracy`

评估指标:
- 平均欧氏距离误差(MAE)
- 均方根误差(RMSE)
- 最大误差

## 结果可视化

[test.py](mdc:test.py)中包含多种可视化函数:

- `plot_loss_comparison`: 绘制损失比较图
- `plot_all_models_loss_comparison`: 绘制所有模型的损失比较图

可视化结果保存在`images/`目录下。

