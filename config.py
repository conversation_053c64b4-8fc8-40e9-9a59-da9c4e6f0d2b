# -*- coding: utf-8 -*-
"""
RFID定位系统配置文件
"""

# 基本配置
CONFIG = {
    'RANDOM_SEED': 42,  # 随机种子，改为更稳定的值
    'K': 6,  # 用于构建图结构的K值
    'TRAIN_LOG': False,  # 启用训练日志
    'PREDICTION_LOG': False,  # 启用预测日志
    'GRID_SEARCH': False,  # 是否启用网格搜索
    'QUICK_SEARCH': False,  # 是否使用快速搜索（减少组合数量）
    'OPEN_MLP': True,  # 启用MLP算法进行比较
    'OPEN_GAT': True,  # 启用GAT算法进行比较
    'OPEN_HETERO': True,  # 启用异构图算法进行比较
    'OPEN_LANDMARC': True,  # 启用LANDMARC算法进行比较
    'HETERO_PERF_MONITOR': False,  # 启用异构图性能监控
    'HETERO_ENSEMBLE_WEIGHT': 0.5,  # 异构图模型在集成时的权重，0.5表示异构图和MLP各占50%
    'USE_TORCH_COMPILE': False,  # 是否使用PyTorch 2.0+的compile功能加速模型

    # 天线位置坐标
    'ANTENNA_LOCATIONS': [
        [0.0, 0.0],  # 天线1 (0,0)
        [0.0, 10.0],  # 天线2 (0,10)
        [10.0, 0.0],  # 天线3 (10,0)
        [10.0, 10.0]  # 天线4 (10,10)
    ],

    # 椅子信息列表
    'CHAIR_INFO': [
        {
            'type': '椅子',
            'position': (6, 6),
            'size': 0.8,
            'material': 'wood'
        },
        {
            'type': '桌子',
            'position': (4, 4),
            'size': 1.5,
            'material': 'wood'
        },
        {
            'type': '金属架',
            'position': (8, 2),
            'size': 1.2,
            'material': 'metal'
        },
        {
            'type': '电脑',
            'position': (2, 7),
            'size': 0.7,
            'material': 'electronic'
        },
        {
            'type': '柜子',
            'position': (7, 8),
            'size': 1.0,
            'material': 'wood'
        },
    ],

    # 数据文件路径
    'REFERENCE_DATA_PATH': 'data/rfid_reference_tags.csv',
    'TEST_DATA_PATH': 'data/rfid_test_tags.csv',

    # 模型默认参数
    'MODEL_PARAMS': {
        'GAT': {
            'K': 6,
            'lr': 0.001,
            'weight_decay': 5e-05,
            'hidden_channels': 96,
            'heads': 2
        },
        'MLP': {'lr': 0.0008,
                'weight_decay': 5e-05,
                'hidden_channels': 96,
                'dropout': 0.1},
        '异构图': {
            'K': 5,
            'lr': 0.005,
            'weight_decay': 1e-05,
            'hidden_channels': 96,
            'heads': 3
        }
    },

    # 训练参数
    'EPOCHS': 1500,
    'PATIENCE': 150,  # 增加早停耐心值

    # 网格搜索参数范围
    'GRID_SEARCH_PARAMS': {
        'k_range': [5, 6, 7, 8],
        'lr_range': [0.0005, 0.0008, 0.001, 0.005],  # 调整学习率范围
        'weight_decay_range': [1e-5, 2e-5, 5e-5, 1e-4],
        'hidden_channels_range': [32, 64, 96, 128],
        'heads_range': [1, 2, 3, 4],  # 增加头数范围
        'dropout_range': [0.1, 0.2, 0.3]
    },

    # 结果保存路径
    'RESULTS_DIR': 'results',
    'MODEL_COMPARISON_IMAGE': 'results/all_models_loss_comparison.png',

    # 边属性权重配置
    'EDGE_ATTR_WEIGHTS': {
        'w1': 1,  # 距离影响权重增加
        'w2': 0,  # RSSI影响权重增加
        'w3': 0,  # 材质和大小影响权重降低，减少椅子材质的影响
    }
}
