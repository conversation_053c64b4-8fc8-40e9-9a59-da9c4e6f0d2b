import torch
import numpy as np
import torch.nn.functional as F
from torch_geometric.data import Data
from sklearn.model_selection import train_test_split
from models.utils.data_loader import load_and_preprocess_test_data

from .model import GATLocalizationModel


def train_gat_model(
    localization, hidden_channels=64, heads=3, lr=0.005, weight_decay=5e-4
):
    """
    训练GAT模型

    参数:
        localization: RFIDLocalization实例
        hidden_channels: 隐藏层通道数
        heads: 注意力头数
        lr: 学习率
        weight_decay: 权重衰减系数

    返回:
        best_val_loss: 最佳验证集损失
        val_avg_distance: 验证集平均误差
        best_model: 最佳模型状态字典
    """
    full_graph_data = create_graph_data(
        localization,
        localization.features_norm,
        localization.labels_norm,
        k=localization.config['K']
    )
    train_mask, val_mask, test_mask = create_data_masks(
        len(localization.features_norm), localization.config, localization.device
    )
    full_graph_data.train_mask = train_mask
    full_graph_data.val_mask = val_mask
    full_graph_data = to_device(full_graph_data, localization.device)
    torch.manual_seed(localization.config['RANDOM_SEED'])
    if torch.cuda.is_available():
        torch.cuda.manual_seed(localization.config['RANDOM_SEED'])
        torch.cuda.manual_seed_all(localization.config['RANDOM_SEED'])
    localization.model = GATLocalizationModel(
        in_channels=full_graph_data.x.shape[1],
        hidden_channels=hidden_channels,
        out_channels=2,
        heads=heads
    ).to(localization.device)
    data_min = torch.as_tensor(
        localization.labels_scaler.data_min_, dtype=torch.float32
    ).to(localization.device)
    data_range = torch.as_tensor(
        localization.labels_scaler.data_range_, dtype=torch.float32
    ).to(localization.device)
    torch.manual_seed(localization.config['RANDOM_SEED'])
    if torch.cuda.is_available():
        torch.cuda.manual_seed(localization.config['RANDOM_SEED'])
        torch.cuda.manual_seed_all(localization.config['RANDOM_SEED'])
    optimizer = torch.optim.Adam(
        localization.model.parameters(), lr=lr, weight_decay=weight_decay
    )
    loss_fn = torch.nn.MSELoss()
    best_val_loss = float('inf')
    best_model = None
    patience = localization.config.get('PATIENCE', 50)
    counter = 0
    best_val_avg_distance = float('inf')
    localization.gat_train_losses = []
    localization.gat_val_losses = []
    for epoch in range(localization.config.get('EPOCHS', 1000)):
        localization.model.train()
        optimizer.zero_grad()
        out = localization.model(full_graph_data)
        train_loss = loss_fn(
            out[full_graph_data.train_mask],
            full_graph_data.y[full_graph_data.train_mask]
        )
        train_loss.backward()
        optimizer.step()
        localization.model.eval()
        with torch.no_grad():
            val_loss = loss_fn(
                out[full_graph_data.val_mask],
                full_graph_data.y[full_graph_data.val_mask]
            )
            out_orig = out * data_range + data_min
            y_orig = full_graph_data.y * data_range + data_min
            train_mask_orig = full_graph_data.train_mask
            val_mask_orig = full_graph_data.val_mask
            if train_mask_orig.sum() > 0:
                train_distances = torch.sqrt(
                    torch.sum((out_orig[train_mask_orig] - y_orig[train_mask_orig])**2,
                              dim=1)
                )
                train_accuracy = (train_distances <
                                  0.3).float().mean().item() * 100
                train_avg_distance = train_distances.mean().item()
            else:
                train_accuracy = 0
                train_avg_distance = 0
            if val_mask_orig.sum() > 0:
                val_distances = torch.sqrt(
                    torch.sum((out_orig[val_mask_orig] - y_orig[val_mask_orig])**2,
                              dim=1)
                )
                val_accuracy = (
                    val_distances < 0.3).float().mean().item() * 100
                val_avg_distance = val_distances.mean().item()
            else:
                val_accuracy = 0
                val_avg_distance = float('inf')
        localization.gat_train_losses.append(train_loss.item())
        localization.gat_val_losses.append(val_loss.item())
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_val_avg_distance = val_avg_distance
            best_model = localization.model.state_dict().copy()
            counter = 0
        else:
            counter += 1
            if counter >= patience:
                if localization.config['TRAIN_LOG']:
                    log_message = (
                        f"轮次 {epoch}\n"
                        f"训练集 - 损失: {train_loss.item():.4f}, 准确率: {train_accuracy:.2f}%, 平均误差: {train_avg_distance:.2f}米\n"
                        f"验证集 - 损失: {val_loss.item():.4f}, 准确率: {val_accuracy:.2f}%, 平均误差: {val_avg_distance:.2f}米\n"
                        f"\n触发早停！在轮次 {epoch} 停止训练\n"
                        f"最佳验证损失: {best_val_loss:.4f}"
                    )
                    localization.train_logger.info(log_message)
                localization.model.load_state_dict(best_model)
                break
        if epoch % 100 == 0 and localization.config['TRAIN_LOG']:
            log_message = (
                f"轮次 {epoch}\n"
                f"训练集 - 损失: {train_loss.item():.4f}, 准确率: {train_accuracy:.2f}%, 平均误差: {train_avg_distance:.2f}米\n"
                f"验证集 - 损失: {val_loss.item():.4f}, 准确率: {val_accuracy:.2f}%, 平均误差: {val_avg_distance:.2f}米"
            )
            localization.train_logger.info(log_message)
    return best_val_loss.item(), best_val_avg_distance, best_model


def evaluate_prediction_GAT_accuracy(localization, test_data=None, num_samples=50):
    """
    评估GAT模型在新标签预测上的准确性，仅使用RSSI特征

    参数:
        localization: RFIDLocalization实例
        test_data: 测试数据，如果为None则从文件加载
        num_samples: 测试样本数量，仅当加载测试数据时使用

    返回:
        predicted_positions_orig: 预测位置
        test_labels: 真实位置
        test_avg_distance: 测试集平均误差距离
        distances.cpu().numpy(): 所有样本的误差列表
    """
    if localization.model is None:
        raise ValueError("模型未训练。请先调用train_gat_model。")

    if test_data is None:
        test_features, test_labels, test_features_np, test_labels_np, _ = load_and_preprocess_test_data(
            localization.config['TEST_DATA_PATH']
        )

        test_features = to_device(test_features, localization.device)
        test_labels = to_device(test_labels, localization.device)

        if num_samples is not None and num_samples < len(test_features):
            test_features = test_features[:num_samples]
            test_labels = test_labels[:num_samples]
    else:
        test_features, test_labels = test_data
        test_features = to_device(
            torch.tensor(
                test_features, dtype=torch.float32), localization.device
        )
        test_labels = to_device(
            torch.tensor(test_labels, dtype=torch.float32), localization.device
        )

    rssi_values = test_features[:, :4]
    rssi_norm = localization.scaler_rssi.transform(rssi_values.cpu().numpy())
    features_new = to_device(
        torch.tensor(rssi_norm, dtype=torch.float32),
        localization.device
    )

    predicted_positions = []
    if localization.mlp_model is None:
        # 如果MLP模型为None，使用MODEL_PARAMS.MLP参数训练一个
        print("GAT评估时发现MLP模型为None，使用MODEL_PARAMS.MLP参数训练MLP模型")
        mlp_params = localization.config['MODEL_PARAMS']['MLP']
        localization.train_mlp_model(
            hidden_channels=mlp_params.get('hidden_channels', 64),
            dropout=mlp_params.get('dropout', 0.1),
            lr=mlp_params.get('lr', 0.005),
            weight_decay=mlp_params.get('weight_decay', 5e-05)
        )
    for i in range(len(features_new)):
        sample_features = features_new[i:i + 1]
        localization.mlp_model.eval()
        with torch.no_grad():
            mlp_pred = localization.mlp_model(sample_features)
        temp_labels = to_device(mlp_pred, localization.device)
        all_features = torch.cat(
            [localization.features_norm, sample_features], dim=0)
        all_labels = torch.cat([localization.labels_norm, temp_labels], dim=0)
        graph_data = create_graph_data(
            localization,
            all_features,
            all_labels,
            k=localization.config['K'],
            pos=localization.labels
        )
        localization.model.eval()
        with torch.no_grad():
            out = localization.model(graph_data)
            pred_idx = len(localization.features_norm)
            pred_pos = out[pred_idx]
            predicted_positions.append(pred_pos)
    predicted_positions = torch.stack(predicted_positions)
    predicted_positions_orig = localization.labels_scaler.inverse_transform(
        predicted_positions.cpu().numpy()
    )
    predicted_positions_orig = to_device(
        torch.tensor(predicted_positions_orig,
                     dtype=torch.float32), localization.device
    )
    distances = torch.sqrt(
        torch.sum((test_labels - predicted_positions_orig)**2, dim=1)
    )
    test_avg_distance = torch.mean(distances).item()

    if localization.config['PREDICTION_LOG']:
        log_message = "\nGAT模型新标签位置预测评估:\n"
        log_message += f"测试样本数量: {len(test_features)}\n"
        log_message += f"平均预测误差: {test_avg_distance:.2f}米\n"
        log_message += f"最大误差: {torch.max(distances).item():.2f}米\n"
        log_message += f"最小误差: {torch.min(distances).item():.2f}米\n"
        log_message += f"误差标准差: {torch.std(distances).item():.2f}米\n"

        accuracies = {}
        for threshold in [0.1, 0.2, 0.3, 0.5, 1.0]:
            accuracy = (distances < threshold).float().mean().item() * 100
            log_message += f"误差 < {threshold}米的准确率: {accuracy:.2f}%\n"
            accuracies[threshold] = accuracy

        # 打印到控制台并写入日志
        localization.prediction_logger.info(log_message)

    return predicted_positions_orig, test_labels, test_avg_distance, distances.cpu().numpy()


def create_graph_data(localization, features_norm, labels_norm, k=None, pos=None):
    """为GAT模型创建图数据"""
    if k is None:
        k = localization.config['K']
    if pos is None:
        pos = localization.labels
    from sklearn.neighbors import kneighbors_graph
    import torch
    import numpy as np
    from torch_geometric.data import Data
    adj_matrix = kneighbors_graph(
        torch.as_tensor(np.hstack([features_norm.cpu()])),
        n_neighbors=k,
        mode='distance',
    )
    adj_matrix_dense = torch.as_tensor(
        adj_matrix.toarray(), dtype=torch.float32)
    edge_index = to_device(
        torch.nonzero(adj_matrix_dense,
                      as_tuple=False).t(), localization.device
    )

    adj_matrix_coo = adj_matrix.tocoo()
    edge_attr = to_device(
        torch.tensor(adj_matrix_coo.data,
                     dtype=torch.float32), localization.device
    )
    return Data(
        x=to_device(features_norm, localization.device),
        edge_index=edge_index,
        edge_attr=edge_attr,
        y=to_device(labels_norm, localization.device),
        pos=pos
    )


def to_device(data, device):
    """将数据移动到指定设备"""
    if isinstance(data, torch.Tensor):
        return data.to(device)
    elif isinstance(data, (list, tuple)):
        return [to_device(x, device) for x in data]
    elif isinstance(data, dict):
        return {k: to_device(v, device) for k, v in data.items()}
    return data


def create_data_masks(num_nodes, config, device, test_size=0.2, val_size=0.2):
    """创建训练、验证和测试集的掩码"""
    import numpy as np
    from sklearn.model_selection import train_test_split
    import torch
    indices = np.arange(num_nodes)
    train_idx, test_idx = train_test_split(
        indices, test_size=test_size, random_state=config['RANDOM_SEED']
    )
    train_idx, val_idx = train_test_split(
        train_idx,
        test_size=val_size / (1 - test_size),
        random_state=config['RANDOM_SEED']
    )
    train_mask = to_device(torch.zeros(num_nodes, dtype=torch.bool), device)
    val_mask = to_device(torch.zeros(num_nodes, dtype=torch.bool), device)
    test_mask = to_device(torch.zeros(num_nodes, dtype=torch.bool), device)
    train_mask[train_idx] = True
    val_mask[val_idx] = True
    test_mask[test_idx] = True
    return train_mask, val_mask, test_mask
