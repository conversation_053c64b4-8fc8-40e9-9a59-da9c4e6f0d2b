---
description:
globs:
alwaysApply: true
---
# 项目概览

本项目是基于图注意力网络（GATv2）的RFID定位系统，集成了多种定位算法，包括GATv2、MLP、KNN、LANDMARC和异构图神经网络。

## 核心文件

- [test.py](mdc:test.py) - 主程序，包含全部模型与实验流程
- [config.py](mdc:config.py) - 配置文件，包含所有可调参数
- [仿真代码.py](mdc:仿真代码.py) - 仿真环境代码

## 主要模型

- [models/gat/model.py](mdc:models/gat/model.py) - GAT模型实现
- [models/mlp/](mdc:models/mlp) - MLP模型实现
- [models/heterogeneous/model.py](mdc:models/heterogeneous/model.py) - 异构图神经网络模型
- [models/landmarc/model.py](mdc:models/landmarc/model.py) - LANDMARC算法实现

## 数据处理

- [models/utils/data_loader.py](mdc:models/utils/data_loader.py) - 数据加载与预处理
- [data/](mdc:data) - 存放原始数据集

## 实验结果

- [results/](mdc:results) - 存放实验结果
- [images/](mdc:images) - 可视化图片（如热力图、布局图等）

## 运行命令
.venv/Scripts/python.exe test.py




