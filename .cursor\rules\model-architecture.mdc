---
description:
globs:
alwaysApply: false
---
# 模型架构

本项目包含多种RFID定位模型，主要分为以下几类：

## 图神经网络模型

### GAT模型
- 基于GATv2实现的图注意力网络
- 主要文件：[models/gat/model.py](mdc:models/gat/model.py)
- 训练函数：`train_gat_model`
- 评估函数：`evaluate_prediction_GAT_accuracy`

### 异构图神经网络模型
- 考虑多种节点类型和边类型的异构图模型
- 主要文件：[models/heterogeneous/model.py](mdc:models/heterogeneous/model.py)
- 训练函数：`train_hetero_model`
- 评估函数：`evaluate_prediction_hetero_accuracy`
- 辅助工具：[models/heterogeneous/utils.py](mdc:models/heterogeneous/utils.py)

## 传统模型

### MLP模型
- 多层感知器模型
- 主要文件：[models/__init__.py](mdc:models/__init__.py)中的`MLPLocalizationModel`
- 训练函数：`train_mlp_model`
- 评估函数：`evaluate_mlp_on_new_data`

### LANDMARC模型
- 基于KNN的传统RFID定位算法
- 主要文件：[models/landmarc/model.py](mdc:models/landmarc/model.py)
- 评估函数：`evaluate_landmarc`

## 模型集成
- 在[test.py](mdc:test.py)中实现了多模型集成方法
- 异构图模型与MLP模型的集成使用`HETERO_ENSEMBLE_WEIGHT`参数控制权重

