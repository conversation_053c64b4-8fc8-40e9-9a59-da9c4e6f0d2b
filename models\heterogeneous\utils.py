# -*- coding: utf-8 -*-
"""
异构图神经网络的工具函数
"""

from config import CONFIG
import torch
import numpy as np
from sklearn.neighbors import kneighbors_graph
from torch_geometric.data import HeteroData
import sys
import os
import time

# 添加项目根目录到系统路径
sys.path.append(
    os.path.dirname(os.path.dirname(
        os.path.dirname(os.path.abspath(__file__))))
)

# 设置随机种子，从config.py中获取RANDOM_SEED
RANDOM_SEED = CONFIG['RANDOM_SEED']
torch.manual_seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)
if torch.cuda.is_available():
    torch.cuda.manual_seed(RANDOM_SEED)
    torch.cuda.manual_seed_all(RANDOM_SEED)


# 扩展HeteroData类，实现迭代器方法
class CustomHeteroData(HeteroData):
    """扩展HeteroData类，添加迭代器方法"""

    def __iter__(self):
        """实现迭代器方法，返回所有节点类型"""
        for node_type in self.node_types:
            yield node_type


# 性能监控类，用于记录异构图相关操作的时间消耗
class HeteroPerformanceMonitor:
    def __init__(self):
        # 图构建时间
        self.graph_build_time = 0
        self.graph_build_count = 0

        # 训练时间
        self.training_start_time = 0
        self.training_total_time = 0
        self.training_iterations = 0

        # 预测时间
        self.prediction_total_time = 0
        self.prediction_count = 0

        # 总运行时间
        self.model_start_time = 0
        self.model_total_time = 0

        # 是否已初始化
        self.initialized = False

    def start_model_timer(self):
        """启动模型总计时器"""
        self.model_start_time = time.time()
        self.initialized = True

    def stop_model_timer(self):
        """停止模型总计时器并记录总时间"""
        if self.initialized:
            self.model_total_time = time.time() - self.model_start_time
            return self.model_total_time
        return 0

    def record_graph_build(self, build_time):
        """记录图构建时间"""
        self.graph_build_time += build_time
        self.graph_build_count += 1

    def start_training(self):
        """开始训练计时"""
        self.training_start_time = time.time()

    def stop_training(self):
        """停止训练计时"""
        if self.training_start_time > 0:
            elapsed = time.time() - self.training_start_time
            self.training_total_time += elapsed
            self.training_iterations += 1
            self.training_start_time = 0
            return elapsed
        return 0

    def record_prediction(self, pred_time):
        """记录预测时间"""
        self.prediction_total_time += pred_time
        self.prediction_count += 1

    def get_summary(self):
        """获取性能统计摘要"""
        avg_training_time = 0
        if self.training_iterations > 0:
            avg_training_time = self.training_total_time / self.training_iterations

        avg_prediction_time = 0
        if self.prediction_count > 0:
            avg_prediction_time = self.prediction_total_time / self.prediction_count

        avg_graph_build_time = 0
        if self.graph_build_count > 0:
            avg_graph_build_time = self.graph_build_time / self.graph_build_count

        return {
            "graph_build_total": self.graph_build_time,
            "graph_build_avg": avg_graph_build_time,
            "training_total": self.training_total_time,
            "training_avg": avg_training_time,
            "prediction_total": self.prediction_total_time,
            "prediction_avg": avg_prediction_time,
            "model_total": self.model_total_time
        }

    def print_summary(self):
        """打印性能统计摘要"""
        summary = self.get_summary()

        print("\n===== 异构图性能统计 =====")
        print(
            f"图构建总时间: {summary['graph_build_total']:.4f}秒, 平均: {summary['graph_build_avg']:.4f}秒/次")
        print(
            f"训练总时间: {summary['training_total']:.4f}秒, 平均: {summary['training_avg']:.4f}秒/迭代")
        print(
            f"预测总时间: {summary['prediction_total']:.4f}秒, 平均: {summary['prediction_avg']:.4f}秒/次")
        print(f"模型总运行时间: {summary['model_total']:.4f}秒")
        print("=========================\n")


# 创建全局性能监控实例
hetero_performance_monitor = HeteroPerformanceMonitor()


def check_obstruction(antenna_pos, tag_pos, chair_pos, chair_size, material='default'):
    """
    检测椅子是否位于天线和标签之间，形成遮挡，并计算信号衰减效应

    参数:
        antenna_pos: 天线位置坐标
        tag_pos: 标签位置坐标
        chair_pos: 椅子位置坐标
        chair_size: 椅子大小
        material: 椅子材质

    返回:
        is_obstructing: 布尔值，表示是否存在遮挡
        attenuation: 信号衰减值
    """
    # 将所有输入转换为numpy数组以便计算
    if isinstance(antenna_pos, torch.Tensor):
        antenna_pos = antenna_pos.cpu().numpy()
    if isinstance(tag_pos, torch.Tensor):
        tag_pos = tag_pos.cpu().numpy()
    if isinstance(chair_pos, torch.Tensor):
        chair_pos = chair_pos.cpu().numpy()

    # 初始化衰减值
    attenuation = 0.0

    # 检查天线和标签位置是否重合
    if np.array_equal(antenna_pos, tag_pos):
        # 如果重合，计算椅子到点的距离
        point_distance = np.sqrt(np.sum((chair_pos - antenna_pos)**2))
        # 当椅子与天线/标签重合点的距离小于椅子大小时，认为存在遮挡
        is_obstructing = point_distance < chair_size
        return is_obstructing, 0.0  # 如果天线和标签重合，衰减为0

    # 如果是垂直或水平路径，直接计算
    if antenna_pos[0] == tag_pos[0]:  # 垂直线
        line_distance = abs(chair_pos[0] - antenna_pos[0])
    elif antenna_pos[1] == tag_pos[1]:  # 水平线
        line_distance = abs(chair_pos[1] - antenna_pos[1])
    else:
        # 线性方程 Ax + By + C = 0
        A = tag_pos[1] - antenna_pos[1]
        B = antenna_pos[0] - tag_pos[0]
        C = tag_pos[0] * antenna_pos[1] - antenna_pos[0] * tag_pos[1]

        # 避免除以零错误
        denominator = np.sqrt(A**2 + B**2)
        if denominator < 1e-10:  # 设置一个很小的阈值
            # 天线和标签位置几乎重合，计算椅子到点的距离
            point_distance = np.sqrt(np.sum((chair_pos - antenna_pos)**2))
            is_obstructing = point_distance < chair_size
            return is_obstructing, 0.0

        line_distance = abs(
            A * chair_pos[0] + B * chair_pos[1] + C) / denominator

    # 计算椅子到天线和标签的投影点
    # 投影点坐标
    t = ((chair_pos[0] - antenna_pos[0]) * (tag_pos[0] - antenna_pos[0]) +
         (chair_pos[1] - antenna_pos[1]) * (tag_pos[1] - antenna_pos[1])) / \
        ((tag_pos[0] - antenna_pos[0])**2 + (tag_pos[1] - antenna_pos[1])**2)

    # 限制t在[0,1]范围内，确保投影点在线段上
    t = max(0, min(1, t))

    proj_x = antenna_pos[0] + t * (tag_pos[0] - antenna_pos[0])
    proj_y = antenna_pos[1] + t * (tag_pos[1] - antenna_pos[1])

    # 检查椅子是否在天线和标签之间的范围内
    # 计算椅子到投影点的距离
    proj_distance = np.sqrt(
        (chair_pos[0] - proj_x)**2 + (chair_pos[1] - proj_y)**2)

    # 判断投影点是否在线段上
    in_segment = (t >= 0 and t <= 1)

    # 检查障碍物是否在源和目标之间的空间范围内
    min_x = min(antenna_pos[0], tag_pos[0])
    max_x = max(antenna_pos[0], tag_pos[0])
    min_y = min(antenna_pos[1], tag_pos[1])
    max_y = max(antenna_pos[1], tag_pos[1])

    in_range = (min_x - chair_size / 2 <= chair_pos[0] <= max_x + chair_size / 2 and
                min_y - chair_size / 2 <= chair_pos[1] <= max_y + chair_size / 2)

    # 判断是否存在遮挡：距离小于椅子大小且投影点在线段上且在范围内
    is_obstructing = proj_distance < chair_size * 1.5 and in_segment and in_range

    # 如果存在遮挡，计算衰减值
    if is_obstructing:
        # 材质影响因子
        if material == 'metal':
            # 金属障碍物会衰减信号
            material_factor = 2.0
        elif material == 'concrete':
            # 混凝土等密实材料有中等衰减
            material_factor = 1.5
        elif material == 'wood':
            # 木材等轻质材料衰减较小
            material_factor = 0.7
        elif material == 'electronic':
            # 电子设备
            material_factor = 1.8
        else:
            # 默认材质
            material_factor = 1.0

        # 使用二次函数模型计算衰减值，更接近物理模型
        # 障碍物越靠近信号路径中心，衰减越大
        proximity_factor = 1 - (proj_distance / chair_size)**2
        proximity_factor = max(0, proximity_factor)  # 确保非负

        # 障碍物大小的影响使用对数关系，避免大型障碍物的过度衰减
        size_effect = 0.8 * np.log(1 + chair_size)

        # 计算确定性的衰减值
        attenuation = size_effect * material_factor * proximity_factor

#         # 添加极小的随机性，使结果更自然但仍保持稳定
#         if np.random.random() < 0.1:  # 只有10%概率添加微小随机效应
#             np.random.seed(
#                 int(chair_pos[0] * 1000 + chair_pos[1] * 100))  # 使随机性可重复
#             random_effect = np.random.uniform(-0.1, 0.1)  # 更小的随机扰动
#             attenuation += random_effect`

    return is_obstructing, attenuation


# 批量检查遮挡关系
def batch_check_obstruction(
    antenna_positions, tag_positions, chair_positions, chair_sizes, chair_materials=None
):
    """
    批量检测椅子是否位于天线和标签之间，形成遮挡

    参数:
        antenna_positions: 所有天线位置坐标 [num_antennas, 2]
        tag_positions: 所有标签位置坐标 [num_tags, 2]
        chair_positions: 所有椅子位置坐标 [num_chairs, 2]
        chair_sizes: 所有椅子大小 [num_chairs]
        chair_materials: 所有椅子材质 [num_chairs]

    返回:
        obstruction_matrix: 形状为 [num_chairs, num_tags, num_antennas] 的布尔矩阵，
                           表示每个(椅子,标签,天线)组合是否存在遮挡
        attenuation_matrix: 形状为 [num_chairs, num_tags, num_antennas] 的衰减矩阵
    """
    # 转换为numpy数组
    if isinstance(antenna_positions, torch.Tensor):
        antenna_positions = antenna_positions.cpu().numpy()
    if isinstance(tag_positions, torch.Tensor):
        tag_positions = tag_positions.cpu().numpy()
    if isinstance(chair_positions, torch.Tensor):
        chair_positions = chair_positions.cpu().numpy()
    if isinstance(chair_sizes, torch.Tensor):
        chair_sizes = chair_sizes.cpu().numpy()

    num_antennas = len(antenna_positions)
    num_tags = len(tag_positions)
    num_chairs = len(chair_positions)

    # 初始化结果矩阵
    obstruction_matrix = np.zeros(
        (num_chairs, num_tags, num_antennas), dtype=bool)
    attenuation_matrix = np.zeros((num_chairs, num_tags, num_antennas))

    # 如果没有提供材质信息，使用默认材质
    if chair_materials is None:
        chair_materials = ['default'] * num_chairs

    # 材质影响因子映射
    material_factors = {
        'metal': 2.0,     # 金属障碍物会衰减信号
        'concrete': 1.5,  # 混凝土等密实材料有中等衰减
        'wood': 0.7,      # 木材等轻质材料衰减较小
        'electronic': 1.8,  # 电子设备
        'default': 1.0    # 默认材质
    }

    # 将材质转换为数值因子
    material_factors_array = np.array(
        [material_factors.get(m, 1.0) for m in chair_materials])

    # 使用广播计算所有组合
    # 重塑数组以便于广播
    # [num_chairs, 1, 1, 2] - 椅子位置
    chairs_reshaped = chair_positions.reshape(num_chairs, 1, 1, 2)
    # [1, num_tags, 1, 2] - 标签位置
    tags_reshaped = tag_positions.reshape(1, num_tags, 1, 2)
    # [1, 1, num_antennas, 2] - 天线位置
    antennas_reshaped = antenna_positions.reshape(1, 1, num_antennas, 2)

    # 椅子大小重塑为 [num_chairs, 1, 1]
    chair_sizes_reshaped = chair_sizes.reshape(num_chairs, 1, 1)

    # 检查天线和标签是否在同一位置
    # 计算天线和标签之间的距离 [1, num_tags, num_antennas]
    tag_antenna_dist = np.sqrt(
        np.sum((tags_reshaped - antennas_reshaped)**2, axis=3))
    same_position = tag_antenna_dist < 1e-10

    # 对于天线和标签在同一位置的情况，计算椅子到该位置的距离
    # [num_chairs, num_tags, num_antennas, 2] - 椅子到标签/天线的向量
    chair_to_point = chairs_reshaped - tags_reshaped
    # [num_chairs, num_tags, num_antennas] - 椅子到标签/天线的距离
    chair_point_dist = np.sqrt(np.sum(chair_to_point**2, axis=3))

    # 当椅子与天线/标签重合点的距离小于椅子大小时，认为存在遮挡
    # 但仅在天线和标签重合的情况下
    obstruction_same_pos = (
        chair_point_dist < chair_sizes_reshaped) & same_position

    # 对于天线和标签不在同一位置的情况
    different_position = ~same_position

    # 计算天线到标签的向量 [1, num_tags, num_antennas, 2]
    antenna_to_tag = tags_reshaped - antennas_reshaped

    # 计算向量长度 [1, num_tags, num_antennas]
    antenna_tag_length = np.sqrt(np.sum(antenna_to_tag**2, axis=3))

    # 单位向量 [1, num_tags, num_antennas, 2]
    with np.errstate(divide='ignore', invalid='ignore'):
        unit_vector = antenna_to_tag / \
            antenna_tag_length.reshape(1, num_tags, num_antennas, 1)
    unit_vector = np.nan_to_num(unit_vector)  # 处理除以零的情况

    # 椅子到天线的向量 [num_chairs, 1, num_antennas, 2]
    chair_to_antenna = chairs_reshaped - antennas_reshaped

    # 计算投影长度 [num_chairs, num_tags, num_antennas]
    projection_length = np.sum(chair_to_antenna * unit_vector, axis=3)

    # 确保投影在线段上 (0 <= t <= 1)
    t = np.clip(projection_length / (antenna_tag_length + 1e-10), 0, 1)

    # 计算投影点 [num_chairs, num_tags, num_antennas, 2]
    projection_points = antennas_reshaped + \
        t.reshape(num_chairs, num_tags, num_antennas, 1) * unit_vector

    # 椅子到投影点的向量 [num_chairs, num_tags, num_antennas, 2]
    chair_to_proj = chairs_reshaped - projection_points

    # 椅子到投影点的距离 [num_chairs, num_tags, num_antennas]
    chair_proj_dist = np.sqrt(np.sum(chair_to_proj**2, axis=3))

    # 判断投影点是否在线段上并且椅子到投影点的距离小于椅子大小的1.5倍
    in_segment = (t >= 0) & (t <= 1)
    close_to_line = chair_proj_dist < (chair_sizes_reshaped * 1.5)

    # 检查障碍物是否在源和目标之间的空间范围内
    # 计算每个(天线,标签)对的边界框
    # [1, num_tags, num_antennas]
    min_x = np.minimum(antennas_reshaped[..., 0], tags_reshaped[..., 0])
    # [1, num_tags, num_antennas]
    max_x = np.maximum(antennas_reshaped[..., 0], tags_reshaped[..., 0])
    # [1, num_tags, num_antennas]
    min_y = np.minimum(antennas_reshaped[..., 1], tags_reshaped[..., 1])
    # [1, num_tags, num_antennas]
    max_y = np.maximum(antennas_reshaped[..., 1], tags_reshaped[..., 1])

    # 检查椅子是否在边界框内 (考虑椅子大小)
    chair_x = chairs_reshaped[..., 0]  # [num_chairs, 1, 1]
    chair_y = chairs_reshaped[..., 1]  # [num_chairs, 1, 1]
    half_size = chair_sizes_reshaped / 2

    in_x_range = (chair_x - half_size <=
                  max_x) & (chair_x + half_size >= min_x)
    in_y_range = (chair_y - half_size <=
                  max_y) & (chair_y + half_size >= min_y)
    in_range = in_x_range & in_y_range

    # 判断是否存在遮挡：距离小于椅子大小且投影点在线段上且在范围内
    is_obstructing = close_to_line & in_segment & in_range & different_position

    # 合并两种情况的遮挡结果
    obstruction_matrix = obstruction_same_pos | is_obstructing

    # 计算衰减值
    # 仅对存在遮挡的情况计算衰减
    # 使用二次函数模型计算衰减值，更接近物理模型
    # 障碍物越靠近信号路径中心，衰减越大
    proximity_factor = np.zeros_like(chair_proj_dist)
    mask = chair_sizes_reshaped > 0  # 避免除以零
    proximity_factor[mask] = 1 - \
        (chair_proj_dist[mask] / chair_sizes_reshaped[mask])**2
    proximity_factor = np.clip(proximity_factor, 0, 1)  # 确保非负且不超过1

    # 障碍物大小的影响使用对数关系，避免大型障碍物的过度衰减
    size_effect = 0.8 * np.log1p(chair_sizes_reshaped)

    # 材质因子广播 [num_chairs, 1, 1]
    material_factors_reshaped = material_factors_array.reshape(
        num_chairs, 1, 1)

    # 计算确定性的衰减值
    attenuation = size_effect * material_factors_reshaped * proximity_factor

    # 仅对存在遮挡的位置应用衰减
    attenuation_matrix = attenuation * obstruction_matrix

    return obstruction_matrix, attenuation_matrix


def compute_multi_antenna_rms_fusion(rssi_features):
    """
    基于欧氏距离的均方根(RMS)方法进行多天线RSSI数据融合

    根据文档要求，使用RMS方法更准确地反映信号的真实强度特性，
    相比简单算术平均能更好地处理多天线系统的信号变化。

    参数:
        rssi_features: 多天线RSSI特征矩阵 (n_tags, n_antennas)

    返回:
        rms_values: 每个标签的RMS融合值 (n_tags,)
    """
    import numpy as np

    rssi_features = np.array(rssi_features)

    # 计算每个标签的多天线RSSI的均方根值
    # RMS = sqrt(mean(x^2))
    rms_values = np.sqrt(np.mean(rssi_features**2, axis=1))

    return rms_values


def compute_rssi_euclidean_distance(rssi_i, rssi_j):
    """
    计算两个标签在多天线RSSI空间中的欧氏距离

    根据文档公式: d_ij = sqrt(sum_m(r_im - r_jm)^2)
    其中 r_im 表示标签i在第m个天线上的RSSI值

    参数:
        rssi_i: 标签i的多天线RSSI值 (n_antennas,)
        rssi_j: 标签j的多天线RSSI值 (n_antennas,)

    返回:
        distance: 欧氏距离值
    """
    import numpy as np

    rssi_i = np.array(rssi_i)
    rssi_j = np.array(rssi_j)

    # 计算多天线RSSI空间中的欧氏距离
    distance = np.sqrt(np.sum((rssi_i - rssi_j)**2))

    return distance


def adaptive_knn_with_local_density(rssi_features, base_k=3, alpha=2, sigma=1.0, min_k=3, max_k=15):
    """
    基于局部密度的自适应K近邻算法

    根据文档中的算法设计，通过分析标签间的RSSI欧氏距离和局部密度分布，
    实现精确高效的动态K值计算。

    算法步骤:
    1. 多天线RSSI距离计算: d_ij = sqrt(sum_m(r_im - r_jm)^2)
    2. 局部密度计算: ρ_i = sum_j exp(-d_ij^2/(2σ^2))
    3. 自适应K值确定: K_i = K_base + floor(α * log(1 + ρ_i))

    参数:
        rssi_features: 多天线RSSI特征矩阵 (n_tags, n_antennas)
        base_k: 基础邻居数量，默认为3
        alpha: 密度敏感系数，控制K值随密度的增长速度，默认为2
        sigma: 密度计算的带宽参数，控制密度计算的敏感度，默认为1.0
        min_k: 最小K值，默认为3
        max_k: 最大K值，默认为15

    返回:
        dynamic_k_values: 每个标签的动态K值列表
        local_densities: 每个标签的局部密度值（用于调试和分析）
    """
    import numpy as np

    rssi_features = np.array(rssi_features)
    n_tags = len(rssi_features)

    # 1. 使用numpy向量化计算所有标签对之间的RSSI欧氏距离矩阵（提高效率）
    # 计算欧氏距离矩阵: ||x_i - x_j||_2
    diff = rssi_features[:, np.newaxis, :] - rssi_features[np.newaxis, :, :]
    distance_matrix = np.sqrt(np.sum(diff**2, axis=2))

    # 2. 向量化计算每个标签的局部密度
    # 调整参数以获得更合理的密度值和K值分布
    adjusted_sigma = max(sigma, np.mean(distance_matrix) * 0.5)  # 自适应调整sigma

    # 使用高斯核函数: exp(-d_ij^2/(2σ^2))
    gaussian_kernel = np.exp(-distance_matrix**2 / (2 * adjusted_sigma**2))

    # 将对角线设为0（排除自己对自己的贡献）
    np.fill_diagonal(gaussian_kernel, 0)

    # 计算每行的和得到局部密度
    local_densities = np.sum(gaussian_kernel, axis=1)

    # 3. 基于局部密度计算自适应K值
    # 调整alpha参数以获得更合理的K值分布
    adjusted_alpha = alpha * 0.3  # 降低alpha以避免K值过大

    # 使用对数函数确保K值平滑增长
    k_adaptive = base_k + \
        np.floor(adjusted_alpha * np.log(1 + local_densities)).astype(int)

    # 约束在[min_k, max_k]范围内
    dynamic_k_values = np.clip(k_adaptive, min_k, max_k).tolist()

    return dynamic_k_values, local_densities


def create_hetero_graph_edges(
    data,
    tag_features,
    tag_positions,
    antenna_positions,
    chair_info=None,
    k=7,
    device='cpu',
    edge_attr_weights=None,
    hetero_perf_monitor=None,
    labels_scaler=None  # 新增参数: 用于反归一化标签坐标
):
    """
    为异构图创建边连接，包括标签-标签、标签-天线和椅子相关的边

    参数:
        data: HeteroData对象，用于添加边
        tag_features: 标签节点的特征（仅包含RSSI特征）
        tag_positions: 标签节点的位置
        antenna_positions: 天线节点的位置
        chair_info: 椅子信息，如果不为None则添加椅子相关的边
        k: KNN的K值
        device: 计算设备
        edge_attr_weights: 边属性计算的权重参数字典
        hetero_perf_monitor: 是否启用异构图性能监控
        labels_scaler: 用于反归一化标签坐标

    返回:
        HeteroData对象
    """
    from config import CONFIG
    import time
    import numpy as np
    from sklearn.neighbors import NearestNeighbors

    # 如果未指定是否启用性能监控，从CONFIG中读取
    if hetero_perf_monitor is None:
        hetero_perf_monitor = CONFIG.get('HETERO_PERF_MONITOR', True)

    # 性能监控字典
    perf_stats = {
        'tag_to_tag': 0,
        'tag_to_antenna': 0,
        'chair_related': 0
    }

    # 如果未提供边属性权重参数，从CONFIG中读取
    if edge_attr_weights is None:
        edge_attr_weights = CONFIG.get('EDGE_ATTR_WEIGHTS', {})

    # 标签节点数量和天线节点数量
    num_tags = len(tag_positions)
    num_antennas = len(antenna_positions)

    # 确保所有数据在同一设备上
    if tag_positions.device != device:
        tag_positions = tag_positions.to(device)
    if antenna_positions.device != device:
        antenna_positions = antenna_positions.to(device)

    # 1. 创建标签到标签的边（使用KNN和自适应K值）
    tag_to_tag_start = time.time()

    # 将tag_positions移动到CPU以便使用sklearn
    tag_positions_np = tag_positions.cpu().numpy()

    # 如果提供了 labels_scaler，则将归一化坐标反归一化到原始尺度
    if labels_scaler is not None:
        try:
            tag_positions_np_original = labels_scaler.inverse_transform(
                tag_positions_np)
        except Exception as e:
            # 如果反归一化失败，回退到归一化坐标并给出警告
            print(
                f"[Warning] labels_scaler.inverse_transform 失败，将使用归一化坐标计算平均距离: {e}")
            tag_positions_np_original = tag_positions_np
    else:
        tag_positions_np_original = tag_positions_np

    # 将反归一化后的坐标转换为张量，保持与输入设备一致
    tag_positions_original_tensor = torch.tensor(
        tag_positions_np_original, dtype=torch.float32, device=device)

    # ===== 基于局部密度的自适应KNN实现 =====

    # 提取RSSI特征用于距离计算
    tag_features_np = tag_features.cpu().numpy()

    # 使用新的自适应KNN算法，基于RSSI信号强度计算
    dynamic_k_values, local_densities = adaptive_knn_with_local_density(
        rssi_features=tag_features_np,  # 使用RSSI特征而非位置坐标
        base_k=3,  # 文档中建议的基础K值
        alpha=2,   # 文档中建议的密度敏感系数
        sigma=1.0,  # 密度计算的带宽参数
        min_k=3,
        max_k=min(15, num_tags - 1)
    )

    # 为了兼容后续代码，计算平均K值作为全局dynamic_k
    dynamic_k = int(np.mean(dynamic_k_values))
    dynamic_k = max(3, min(min(15, num_tags - 1), dynamic_k))
    # 3. 基于RSSI距离的邻居查找
    # 使用RSSI特征进行邻居搜索，而不是位置坐标
    if num_tags > 1000:
        algorithm = 'auto'  # 让sklearn自动选择最优算法
    elif num_tags > 100:
        algorithm = 'kd_tree'
    else:
        algorithm = 'ball_tree'

    # 使用RSSI特征进行KNN搜索
    nbrs = NearestNeighbors(n_neighbors=min(dynamic_k + 1, num_tags),
                            algorithm=algorithm).fit(tag_features_np)
    rssi_distances, indices = nbrs.kneighbors(tag_features_np)

    # 4. 基于RSSI距离的边构建和线性权重计算
    edge_index_list = []
    edge_attr_list = []

    for i in range(num_tags):
        # 使用每个点的动态K值
        point_k = dynamic_k_values[i] if i < len(
            dynamic_k_values) else dynamic_k
        actual_neighbors = min(len(indices[i]) - 1, point_k)

        # 收集当前标签的邻居距离和排名
        neighbor_distances = []
        neighbor_ranks = []

        for j in range(1, actual_neighbors + 1):  # 跳过自己
            neighbor_idx = indices[i][j]
            if neighbor_idx == i:  # 额外安全检查
                continue

            rssi_distance = rssi_distances[i][j]

            neighbor_distances.append(rssi_distance)
            neighbor_ranks.append(j)

        # 使用简化的权重计算方法
        if neighbor_distances:
            # 添加边和权重
            for rank, distance in enumerate(neighbor_distances, 1):
                neighbor_idx = indices[i][rank-1]

                # 基于距离的简单权重计算
                weight = 1.0 / (1.0 + distance**2)

                # 添加双向边
                edge_index_list.append([i, neighbor_idx])
                edge_index_list.append([neighbor_idx, i])

                edge_attr_list.append([weight])
                edge_attr_list.append([weight])

    # 转换为张量
    if edge_index_list:
        tag_to_tag_edge_index = torch.tensor(
            edge_index_list, dtype=torch.long, device=device).t().contiguous()
        tag_to_tag_edge_attr = torch.tensor(
            edge_attr_list, dtype=torch.float32, device=device)

        # 添加到数据对象
        data['tag', 'to', 'tag'].edge_index = tag_to_tag_edge_index
        data['tag', 'to', 'tag'].edge_attr = tag_to_tag_edge_attr

    perf_stats['tag_to_tag'] = time.time() - tag_to_tag_start

    # 2. 创建标签到天线的边 - 仅使用RSSI信息
    tag_to_antenna_start = time.time()

    # 计算每个标签到每个天线的距离
    tag_to_antenna_edges = []
    tag_to_antenna_attrs = []
    antenna_to_tag_edges = []
    antenna_to_tag_attrs = []

    # 获取RSSI信息
    has_rssi = tag_features.shape[1] >= 4

    # 遍历每个标签和天线对
    for i in range(num_tags):
        for j in range(num_antennas):
            # 添加双向边
            tag_to_antenna_edges.append([i, j])
            antenna_to_tag_edges.append([j, i])

            # 计算欧几里得距离 (使用反归一化坐标)
            distance = torch.sqrt(torch.sum(
                (tag_positions_original_tensor[i] - antenna_positions[j])**2)).item()

            # 基于距离的权重 - 使用平滑的衰减函数
            distance_weight = 1.0 / (1.0 + distance**2)

            # 如果有RSSI信息，使用它来调整权重
            if has_rssi:
                # 提取对应天线的RSSI信息
                rssi_value = tag_features[i, j].item()  # 假设前4个特征是RSSI

                # 基于RSSI的权重 - RSSI越强，权重越大
                rssi_weight = 1.0 / (1.0 + np.exp(-rssi_value * 5.0))

                # 组合权重
                w1 = edge_attr_weights.get('w1', 0)  # 距离影响权重
                w2 = edge_attr_weights.get('w2', 0)  # RSSI影响权重

                # 最终权重 - 结合距离和RSSI
                weight = w1 * distance_weight + w2 * rssi_weight
            else:
                # 如果没有RSSI信息，只使用距离权重
                weight = distance_weight

            # 添加边属性
            tag_to_antenna_attrs.append([weight])
            antenna_to_tag_attrs.append([weight])

    # 转换为张量
    tag_to_antenna_edge_index = torch.tensor(
        tag_to_antenna_edges, dtype=torch.long, device=device).t().contiguous()
    tag_to_antenna_edge_attr = torch.tensor(
        tag_to_antenna_attrs, dtype=torch.float32, device=device)

    antenna_to_tag_edge_index = torch.tensor(
        antenna_to_tag_edges, dtype=torch.long, device=device).t().contiguous()
    antenna_to_tag_edge_attr = torch.tensor(
        antenna_to_tag_attrs, dtype=torch.float32, device=device)

    # 添加到数据对象
    data['tag', 'to', 'antenna'].edge_index = tag_to_antenna_edge_index
    data['tag', 'to', 'antenna'].edge_attr = tag_to_antenna_edge_attr

    data['antenna', 'to', 'tag'].edge_index = antenna_to_tag_edge_index
    data['antenna', 'to', 'tag'].edge_attr = antenna_to_tag_edge_attr

    perf_stats['tag_to_antenna'] = time.time() - tag_to_antenna_start

    # 3. 创建与椅子相关的边
    chair_related_start = time.time()

    if chair_info is not None and hasattr(data, 'chair'):
        # 获取椅子位置和特性
        chair_positions = data['chair'].pos
        chair_features = data['chair'].x
        num_chairs = len(chair_positions)

        # 3.1 创建标签到椅子的边
        tag_to_chair_edges = []
        tag_to_chair_attrs = []
        chair_to_tag_edges = []
        chair_to_tag_attrs = []

        # 使用矩阵运算一次性计算所有距离
        tag_positions_expanded_orig = tag_positions_original_tensor.unsqueeze(
            1)  # [num_tags,1,2]
        distances = torch.sqrt(torch.sum(
            (tag_positions_expanded_orig - chair_positions.unsqueeze(0))**2, dim=2))  # [num_tags, num_chairs]

        # 为每个标签-椅子对创建边
        for i in range(num_tags):
            for j in range(num_chairs):
                # 计算距离
                distance = distances[i, j].item()
                chair_size = chair_features[j, 0].item()  # 椅子大小
                material_coef = chair_features[j, 1].item()  # 材质系数

                # 根据距离、椅子大小和材质计算权重
                # 距离影响 - 改进衰减函数
                distance_weight = 1.0 / (1.0 + (distance / chair_size)**2)

                # 大小和材质影响 - 材质系数越高，对信号的影响越大
                size_material_weight = chair_size * material_coef / 5.0

                # 组合权重
                w1 = edge_attr_weights.get('w1', 0)  # 距离影响权重
                w3 = edge_attr_weights.get('w3', 0)  # 材质和大小影响权重
                weight = w1 * distance_weight + w3 * size_material_weight

                # 添加双向边
                tag_to_chair_edges.append([i, j])
                tag_to_chair_attrs.append([weight])

                chair_to_tag_edges.append([j, i])
                chair_to_tag_attrs.append([weight])

        # 转换为张量
        if tag_to_chair_edges:
            tag_to_chair_edge_index = torch.tensor(
                tag_to_chair_edges, dtype=torch.long, device=device).t().contiguous()
            tag_to_chair_edge_attr = torch.tensor(
                tag_to_chair_attrs, dtype=torch.float32, device=device)

            chair_to_tag_edge_index = torch.tensor(
                chair_to_tag_edges, dtype=torch.long, device=device).t().contiguous()
            chair_to_tag_edge_attr = torch.tensor(
                chair_to_tag_attrs, dtype=torch.float32, device=device)

            # 添加到数据对象
            data['tag', 'to', 'chair'].edge_index = tag_to_chair_edge_index
            data['tag', 'to', 'chair'].edge_attr = tag_to_chair_edge_attr

            data['chair', 'to', 'tag'].edge_index = chair_to_tag_edge_index
            data['chair', 'to', 'tag'].edge_attr = chair_to_tag_edge_attr

        # 3.2 创建椅子到天线的边
        chair_to_antenna_edges = []
        chair_to_antenna_attrs = []
        antenna_to_chair_edges = []
        antenna_to_chair_attrs = []

        # 使用矩阵运算一次性计算所有距离
        chair_positions_expanded = chair_positions.unsqueeze(
            1)  # [num_chairs, 1, 2]
        antenna_positions_expanded = antenna_positions.unsqueeze(
            0)  # [1, num_antennas, 2]
        distances = torch.sqrt(torch.sum(
            (chair_positions_expanded - antenna_positions_expanded)**2, dim=2))  # [num_chairs, num_antennas]

        # 为每个椅子-天线对创建边
        for i in range(num_chairs):
            for j in range(num_antennas):
                # 计算距离
                distance = distances[i, j].item()
                chair_size = chair_features[i, 0].item()  # 椅子大小
                material_coef = chair_features[i, 1].item()  # 材质系数

                # 根据距离、椅子大小和材质计算权重
                # 距离影响 - 改进衰减函数
                distance_weight = 1.0 / (1.0 + (distance / chair_size)**2)

                # 大小和材质影响 - 材质系数越高，对信号的影响越大
                size_material_weight = chair_size * material_coef / 5.0

                # 组合权重
                w1 = edge_attr_weights.get('w1', 0)  # 距离影响权重
                w3 = edge_attr_weights.get('w3', 0)  # 材质和大小影响权重
                weight = w1 * distance_weight + w3 * size_material_weight

                # 添加双向边
                chair_to_antenna_edges.append([i, j])
                chair_to_antenna_attrs.append([weight])

                antenna_to_chair_edges.append([j, i])
                antenna_to_chair_attrs.append([weight])

        # 转换为张量并添加到数据对象
        if chair_to_antenna_edges:
            chair_to_antenna_edge_index = torch.tensor(
                chair_to_antenna_edges, dtype=torch.long, device=device).t().contiguous()
            chair_to_antenna_edge_attr = torch.tensor(
                chair_to_antenna_attrs, dtype=torch.float32, device=device)

            antenna_to_chair_edge_index = torch.tensor(
                antenna_to_chair_edges, dtype=torch.long, device=device).t().contiguous()
            antenna_to_chair_edge_attr = torch.tensor(
                antenna_to_chair_attrs, dtype=torch.float32, device=device)

            # 添加到数据对象
            data['chair', 'to', 'antenna'].edge_index = chair_to_antenna_edge_index
            data['chair', 'to', 'antenna'].edge_attr = chair_to_antenna_edge_attr

            data['antenna', 'to', 'chair'].edge_index = antenna_to_chair_edge_index
            data['antenna', 'to', 'chair'].edge_attr = antenna_to_chair_edge_attr

        # 3.3 创建椅子遮挡标签的关系 - 增强障碍物遮挡建模
        obstructs_edges = []
        obstructs_attrs = []

        # 创建向量从标签到天线
        for i in range(num_tags):
            for j in range(num_antennas):
                tag_pos = tag_positions_original_tensor[i]
                antenna_pos = antenna_positions[j]
                tag_to_antenna_vec = antenna_pos - tag_pos
                tag_to_antenna_dist = torch.norm(tag_to_antenna_vec).item()

                # 单位向量
                unit_vec = tag_to_antenna_vec / tag_to_antenna_dist

                # 检查每个椅子是否在标签和天线之间形成遮挡
                for k in range(num_chairs):
                    chair_pos = chair_positions[k]
                    chair_size = chair_features[k, 0].item()
                    material_coef = chair_features[k, 1].item()

                    # 计算椅子到标签-天线连线的距离
                    # 使用点到线段的距离公式
                    t = torch.dot(chair_pos - tag_pos, unit_vec)

                    # 确保t在线段范围内 (0 <= t <= tag_to_antenna_dist)
                    if t > 0 and t < tag_to_antenna_dist:
                        # 计算椅子到线的投影点
                        proj_point = tag_pos + t * unit_vec
                        # 计算椅子到投影点的距离
                        obstacle_distance = torch.norm(
                            chair_pos - proj_point).item()

                        # 如果距离小于椅子大小的某个倍数，认为构成遮挡
                        if obstacle_distance < chair_size * 1.5:
                            # 计算遮挡效果
                            # 1. 距离投影点越近，遮挡越强
                            distance_factor = 1.0 - \
                                min(1.0, obstacle_distance / (chair_size * 1.5))

                            # 2. 材质影响 - 金属等材质遮挡更强
                            material_factor = material_coef

                            # 3. 椅子大小影响
                            size_factor = chair_size / 2.0

                            # 4. 标签到投影点的距离比例 - 越靠近标签遮挡效果越大
                            position_factor = 1.0 - (t / tag_to_antenna_dist)

                            # 综合计算遮挡权重
                            obstruction_weight = (
                                0.4 * distance_factor +
                                0.3 * material_factor +
                                0.2 * size_factor +
                                0.1 * position_factor
                            )

                            # 添加遮挡边
                            obstructs_edges.append([k, i])  # 椅子遮挡标签
                            obstructs_attrs.append([obstruction_weight])

            # 转换为张量并添加到数据对象
            if obstructs_edges:
                obstructs_edge_index = torch.tensor(
                    obstructs_edges, dtype=torch.long, device=device).t().contiguous()
                obstructs_edge_attr = torch.tensor(
                    obstructs_attrs, dtype=torch.float32, device=device)

                # 添加到数据对象
                data['chair', 'obstructs', 'tag'].edge_index = obstructs_edge_index
                data['chair', 'obstructs', 'tag'].edge_attr = obstructs_edge_attr

    perf_stats['chair_related'] = time.time() - chair_related_start

    # 始终返回元组 (data, perf_stats)
    return data


def create_heterogeneous_graph_data(
    features_norm,
    labels_norm,
    antenna_positions,
    k=7,
    device='cpu',
    chair_info=None,
    edge_attr_weights=None,
    hetero_perf_monitor=None,
    labels_scaler=None  # 新增参数
):
    """
    创建异构图数据

    参数:
        features_norm: 标准化后的特征
        labels_norm: 标准化后的标签
        antenna_positions: 天线位置
        k: KNN的K值
        device: 计算设备
        chair_info: 椅子信息，如果不为None则添加椅子节点
        edge_attr_weights: 边属性权重
        hetero_perf_monitor: 是否启用异构图性能监控
        labels_scaler: 用于反归一化标签坐标

    返回:
        data: 异构图数据
    """
    import torch
    from torch_geometric.data import HeteroData
    import time
    import numpy as np

    # 如果未提供边属性权重参数，从CONFIG中读取
    if edge_attr_weights is None:
        edge_attr_weights = CONFIG.get('EDGE_ATTR_WEIGHTS', {})

    # 记录开始时间
    start_time = time.time()

    # 创建异构图数据对象
    data = CustomHeteroData()

    # 标签节点特征
    data['tag'].x = features_norm.clone()

    # 标签节点位置
    data['tag'].pos = labels_norm.clone()
    # 添加标签掩码，用于区分训练集、验证集和测试集
    data['tag'].tag_mask = torch.zeros(
        len(features_norm), dtype=torch.bool, device=device)

    # 天线节点特征和位置
    # 天线特征使用全1向量，表示天线的存在
    data['antenna'].x = torch.ones(
        (len(antenna_positions), features_norm.shape[1]), device=device)
    data['antenna'].pos = antenna_positions.clone()

    # 如果提供了椅子信息，添加椅子节点
    if chair_info is not None:
        # 创建椅子特征和位置
        num_chairs = len(chair_info)
        chair_features = []
        chair_positions = []
        chair_sizes = []
        chair_materials = []

        for chair in chair_info:
            # 椅子特征：使用位置、大小和材质编码
            chair_pos = chair['position']
            chair_size = chair['size']
            chair_material = chair['material']

            # 材质编码
            material_code = 0.0  # 默认材质
            if chair_material == 'wood':
                material_code = 0.25
            elif chair_material == 'metal':
                material_code = 0.5
            elif chair_material == 'concrete':
                material_code = 0.75
            elif chair_material == 'electronic':
                material_code = 1.0

            # 创建椅子特征向量
            chair_feature = torch.tensor(
                [chair_pos[0]/10.0, chair_pos[1]/10.0, chair_size/2.0, material_code] +
                [0.0] * (features_norm.shape[1] - 4),  # 填充剩余维度
                device=device
            )
            chair_features.append(chair_feature)

            # 记录椅子位置和大小
            chair_positions.append(chair_pos)
            chair_sizes.append(chair_size)
            chair_materials.append(chair_material)

        # 转换为张量
        chair_features = torch.stack(chair_features)
        chair_positions = torch.tensor(chair_positions, device=device)
        chair_sizes = torch.tensor(chair_sizes, device=device)

        # 添加到数据对象
        data['chair'].x = chair_features
        data['chair'].pos = chair_positions
        data['chair'].size = chair_sizes
        data['chair'].materials = chair_materials

    # 创建边连接
    data = create_hetero_graph_edges(
        data,
        features_norm,
        labels_norm,
        antenna_positions,
        chair_info,
        k=k,
        device=device,
        edge_attr_weights=edge_attr_weights,
        hetero_perf_monitor=hetero_perf_monitor,
        labels_scaler=labels_scaler
    )

    # 记录图构建时间
    graph_build_time = time.time() - start_time
    if hetero_perf_monitor:
        hetero_performance_monitor.record_graph_build(graph_build_time)

    return data


def add_new_node_to_hetero_graph(
    hetero_data,
    new_features,
    initial_position,
    k=5,
    device='cpu',
    edge_attr_weights=None,
    hetero_perf_monitor=None,
    labels_scaler=None  # 新增参数
):
    """
    向已有的异构图中添加新节点

    参数:
        hetero_data: 已有的异构图数据
        new_features: 新节点的特征（仅包含RSSI特征）
        initial_position: 新节点的初始位置估计
        k: KNN的K值
        device: 计算设备
        edge_attr_weights: 边属性权重
        hetero_perf_monitor: 是否启用异构图性能监控
        labels_scaler: 用于反归一化标签坐标

    返回:
        更新后的HeteroData对象
    """
    import torch
    import time
    from config import CONFIG

    # 如果未指定是否启用性能监控，从CONFIG中读取
    if hetero_perf_monitor is None:
        hetero_perf_monitor = CONFIG.get('HETERO_PERF_MONITOR', True)

    # 记录开始时间
    start_time = time.time()

    # 创建新数据对象
    new_data = CustomHeteroData()

    # 获取原有标签节点和天线节点
    original_tag_features = hetero_data['tag'].x
    original_tag_positions = hetero_data['tag'].pos  # 使用pos而不是y

    antenna_features = hetero_data['antenna'].x
    antenna_positions = hetero_data['antenna'].pos  # 使用pos而不是y

    # 检查是否存在椅子节点
    chair_info = None
    try:
        # 尝试访问椅子节点特征
        chair_features = hetero_data['chair'].x
        chair_positions = hetero_data['chair'].pos  # 使用pos而不是y
        chair_sizes = hetero_data['chair'].size
        chair_materials = hetero_data['chair'].materials

        # 设置椅子节点特征和位置
        new_data['chair'].x = chair_features
        new_data['chair'].pos = chair_positions
        new_data['chair'].size = chair_sizes
        new_data['chair'].materials = chair_materials

        # 构建椅子信息列表
        chair_info = []
        for i in range(len(chair_positions)):
            chair_info.append({
                'position': chair_positions[i].tolist(),
                'size': chair_sizes[i].item(),
                'material': chair_materials[i]
            })
    except (KeyError, AttributeError):
        # 如果发生KeyError或AttributeError，说明没有椅子节点
        pass

    # 添加新节点
    num_original_tags = len(original_tag_features)

    # 确保数据类型和维度一致
    if not isinstance(new_features, torch.Tensor):
        new_features = torch.tensor(new_features, dtype=torch.float32)
    if new_features.dim() == 1:
        new_features = new_features.unsqueeze(0)

    if not isinstance(initial_position, torch.Tensor):
        initial_position = torch.tensor(initial_position, dtype=torch.float32)
    if initial_position.dim() == 1:
        initial_position = initial_position.unsqueeze(0)

    # 转移到指定设备
    new_features = new_features.to(device)
    initial_position = initial_position.to(device)

    # 检查新特征的维度是否与原始特征匹配
    if new_features.shape[1] != original_tag_features.shape[1]:
        # 如果维度不匹配，简单填充零
        padding = torch.zeros((new_features.shape[0],
                              original_tag_features.shape[1] - new_features.shape[1]),
                              device=device)
        new_features = torch.cat([new_features, padding], dim=1)

    # 合并标签节点
    all_tag_features = torch.cat([original_tag_features, new_features], dim=0)
    all_tag_positions = torch.cat(
        [original_tag_positions, initial_position], dim=0)

    # 设置节点特征和位置
    new_data['tag'].x = all_tag_features
    new_data['tag'].pos = all_tag_positions
    new_data['antenna'].x = antenna_features
    new_data['antenna'].pos = antenna_positions

    # 添加tag_mask来标记新节点
    tag_mask = torch.zeros(len(all_tag_features),
                           dtype=torch.bool, device=device)
    tag_mask[num_original_tags:] = True  # 将新添加的节点标记为True
    new_data['tag'].tag_mask = tag_mask

    # 创建异构图的边
    new_data = create_hetero_graph_edges(
        new_data,
        all_tag_features,
        all_tag_positions,
        antenna_positions,
        chair_info,
        k=k,
        device=device,
        edge_attr_weights=edge_attr_weights,
        hetero_perf_monitor=hetero_perf_monitor,
        labels_scaler=labels_scaler
    )

    # 记录添加节点的时间
    build_time = time.time() - start_time

    # 如果启用性能监控，记录到全局监控器
    if hetero_perf_monitor:
        hetero_performance_monitor.record_graph_build(build_time)

    return new_data

# 添加训练性能监控函数


def start_hetero_training_timer():
    """开始记录训练时间"""
    global hetero_performance_monitor
    hetero_performance_monitor.start_training()


def stop_hetero_training_timer():
    """停止记录训练时间，返回本次训练耗时"""
    global hetero_performance_monitor
    return hetero_performance_monitor.stop_training()

# 添加预测性能监控函数


def record_hetero_prediction_time(start_time):
    """记录预测时间

    参数:
        start_time: 预测开始时间

    返回:
        预测耗时
    """
    global hetero_performance_monitor
    pred_time = time.time() - start_time
    hetero_performance_monitor.record_prediction(pred_time)
    return pred_time

# 添加总模型计时控制函数


def start_hetero_model_timer():
    """开始记录异构图模型总运行时间"""
    global hetero_performance_monitor
    hetero_performance_monitor.start_model_timer()


def stop_hetero_model_timer():
    """停止记录异构图模型总运行时间，返回总耗时"""
    global hetero_performance_monitor
    return hetero_performance_monitor.stop_model_timer()

# 添加性能摘要输出函数


def print_hetero_performance_summary():
    """打印异构图模型性能统计摘要"""
    from config import CONFIG
    if CONFIG.get('HETERO_PERF_MONITOR', False):
        global hetero_performance_monitor
        # 移除 hetero_performance_monitor.print_summary()
        # 保留函数结构，但不输出内容
        pass
