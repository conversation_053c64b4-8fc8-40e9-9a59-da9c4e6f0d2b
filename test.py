# -*- coding: utf-8 -*-
"""
使用图注意力网络(GAT)的RFID定位方法
"""

import warnings
import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
import matplotlib.pyplot as plt
import networkx as nx
from sklearn.neighbors import kneighbors_graph, KNeighborsRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MinMaxScaler
from torch_geometric.data import Data
from torch_geometric.utils import from_scipy_sparse_matrix, to_undirected
from torch_geometric.nn import GATv2Conv
import itertools
import time
import os
import logging
from datetime import datetime
import matplotlib.font_manager as fm
import matplotlib

from config import CONFIG
from models import MLPLocalizationModel, train_mlp_model, evaluate_mlp_on_new_data
from models.utils.data_loader import load_and_preprocess_test_data
from models.landmarc.model import landmarc_localization, evaluate_landmarc
from models.heterogeneous import HeterogeneousGNNModel, create_heterogeneous_graph_data, add_new_node_to_hetero_graph, train_hetero_model as train_hetero_model_func
from models.gat.model import GATLocalizationModel
from models.gat.utils import train_gat_model, evaluate_prediction_GAT_accuracy, create_data_masks, to_device
from models.utils.grid_search import run_grid_search
from models.heterogeneous.utils import record_hetero_prediction_time

warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings(
    "ignore", message="The verbose parameter is deprecated")

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

# 修改matplotlib配置解决中文显示问题
matplotlib.rcParams['font.sans-serif'] = ['SimHei',
                                          'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['font.family'] = 'sans-serif'
matplotlib.rcParams['axes.unicode_minus'] = False

# 寻找可用的中文字体并输出
try:
    font_paths = []
    for font in matplotlib.font_manager.findSystemFonts():
        try:
            font_name = matplotlib.font_manager.FontProperties(
                fname=font).get_name()
            if any(cn_font in font_name for cn_font in ['SimHei', 'Microsoft YaHei', 'SimSun']):
                font_paths.append((font_name, font))
        except:
            pass

    if font_paths:
        font_path = font_paths[0][1]
        # 添加字体文件到matplotlib
        matplotlib.font_manager.fontManager.addfont(font_path)
except Exception as e:
    print(f"字体检测过程中出现错误: {e}")


def can_use_torch_compile():
    """
    检查当前环境是否支持torch.compile功能，包括核心依赖的Triton库

    返回:
        bool: 如果支持返回True，否则返回False
    """
    # 首先检查PyTorch版本是否支持compile
    if not hasattr(torch, 'compile'):
        print("当前PyTorch版本不支持torch.compile，需要PyTorch 2.0以上版本")
        return False

    # 检查是否有CUDA支持
    if not torch.cuda.is_available():
        print("没有可用的CUDA设备，torch.compile使用GPU加速可能受限")

    # 检查Triton库是否存在
    try:
        import importlib
        triton_available = importlib.util.find_spec("triton") is not None
        if not triton_available:
            print("缺少Triton库，torch.compile可能无法正常工作。可通过pip install triton安装")
            return False
    except:
        print("检查Triton库时出错，torch.compile可能无法正常工作")
        return False

    # 实际测试编译功能
    try:
        # 创建一个小的测试模型并尝试编译
        test_model = torch.nn.Linear(10, 10)
        compiled_model = torch.compile(test_model, mode='reduce-overhead')

        # 生成一些测试数据并尝试运行
        test_input = torch.randn(1, 10)
        if torch.cuda.is_available():
            test_input = test_input.cuda()
            test_model = test_model.cuda()
            compiled_model = compiled_model.cuda()

        # 执行一次前向传播测试编译是否正常工作
        with torch.no_grad():
            _ = compiled_model(test_input)

        print("torch.compile功能测试成功，可以安全使用")
        return True
    except Exception as e:
        print(f"torch.compile测试失败: {e}")
        print("禁用torch.compile功能以确保模型正常运行")
        return False


class RFIDLocalization:
    """使用GAT进行RFID定位的主类"""

    def __init__(self, config=None):
        """使用配置进行初始化"""
        self.config = config or CONFIG
        self.device = torch.device(
            'cuda' if torch.cuda.is_available() else 'cpu')
        self.set_random_seed()

        os.makedirs('log', exist_ok=True)
        self.log_time = datetime.now().strftime("%Y%m%d_%H%M%S")

        if self.config['TRAIN_LOG']:
            self.train_logger = logging.getLogger('train_log')
            self.train_logger.setLevel(logging.INFO)
            train_handler = logging.FileHandler(
                f'log/train_{self.log_time}.log', encoding='utf-8'
            )
            train_handler.setFormatter(
                logging.Formatter('%(asctime)s - %(message)s'))
            self.train_logger.addHandler(train_handler)

        if self.config['PREDICTION_LOG']:
            self.prediction_logger = logging.getLogger('prediction_log')
            self.prediction_logger.setLevel(logging.INFO)
            prediction_handler = logging.FileHandler(
                f'log/prediction_{self.log_time}.log', encoding='utf-8'
            )
            prediction_handler.setFormatter(
                logging.Formatter('%(asctime)s - %(message)s')
            )
            self.prediction_logger.addHandler(prediction_handler)

        self.df = None
        self.features = None
        self.labels = None
        self.features_norm = None
        self.labels_norm = None
        self.pos = None
        self.scaler_rssi = MinMaxScaler()
        self.scaler_phase = MinMaxScaler()
        self.labels_scaler = MinMaxScaler()
        self.model = None
        self.mlp_model = None
        self.hetero_model = None  # 异构图模型

        self.antenna_locations = torch.tensor(
            self.config['ANTENNA_LOCATIONS'], dtype=torch.float32
        ).to(self.device)

        self.gat_train_losses = []
        self.gat_val_losses = []
        self.mlp_train_losses = []
        self.mlp_val_losses = []
        self.hetero_train_losses = []  # 异构图模型训练损失
        self.hetero_val_losses = []  # 异构图模型验证损失

        self.use_torch_compile = self.config.get(
            'USE_TORCH_COMPILE', False) and can_use_torch_compile()
        if self.config.get('USE_TORCH_COMPILE', False) and not self.use_torch_compile:
            print("配置中启用了torch.compile但环境不支持，已自动禁用")

        self.load_data()

    def set_random_seed(self):
        """设置随机种子以确保可重现性"""
        np.random.seed(self.config['RANDOM_SEED'])
        torch.manual_seed(self.config['RANDOM_SEED'])
        if torch.cuda.is_available():
            torch.cuda.manual_seed(self.config['RANDOM_SEED'])
            torch.cuda.manual_seed_all(self.config['RANDOM_SEED'])
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False

    def load_data(self):
        """加载和预处理数据"""
        features, labels, features_np, labels_np, _ = load_and_preprocess_test_data(
            self.config['REFERENCE_DATA_PATH']
        )

        self.features = to_device(features, self.device)
        self.labels = to_device(labels, self.device)

        rssi_norm = self.scaler_rssi.fit_transform(features_np[:, :4])
        self.features_norm = to_device(
            torch.tensor(rssi_norm, dtype=torch.float32),
            self.device
        )

        self.labels_norm = to_device(
            torch.tensor(
                self.labels_scaler.fit_transform(self.labels.cpu().numpy()),
                dtype=torch.float32
            ), self.device
        )

        self.pos = {
            i: (self.labels[i][0].item(), self.labels[i][1].item())
            for i in range(len(self.labels))
        }

    def train_gat_model(self, hidden_channels=64, heads=3, lr=0.005, weight_decay=5e-4):
        """训练GAT模型，支持超参数传递，返回验证集损失、平均误差和最佳模型参数"""
        val_avg_distance, val_loss, best_model = train_gat_model(
            self, hidden_channels, heads, lr, weight_decay)
        # 确保模型使用最佳状态
        self.model.load_state_dict(best_model)
        return val_loss, val_avg_distance, best_model

    def evaluate_prediction_GAT_accuracy(self, test_data=None, num_samples=50):
        return evaluate_prediction_GAT_accuracy(self, test_data, num_samples)

    def train_mlp_model(
        self, hidden_channels=128, dropout=0.1, lr=0.001, weight_decay=0.0005
    ):
        """训练MLP模型，支持超参数传递，返回验证集损失、平均误差和最佳模型参数"""
        # 调用models包中的train_mlp_model函数
        return train_mlp_model(
            self,
            hidden_channels=hidden_channels,
            dropout=dropout,
            lr=lr,
            weight_decay=weight_decay
        )

    def plot_loss_comparison(self, save_path=None):
        """绘制MLP和GAT的训练和验证损失对比图"""
        if len(self.mlp_train_losses) == 0 or len(self.gat_train_losses) == 0:
            return

        plt.figure(figsize=(12, 8))

        # 确保目录存在
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 绘制训练损失
        plt.subplot(2, 1, 1)
        max_epoch = 50
        epochs_mlp = range(1, min(len(self.mlp_train_losses), max_epoch) + 1)
        epochs_gat = range(1, min(len(self.gat_train_losses), max_epoch) + 1)
        plt.plot(
            epochs_mlp, self.mlp_train_losses[:max_epoch], 'b-', label='MLP训练损失')
        plt.plot(
            epochs_gat, self.gat_train_losses[:max_epoch], 'r-', label='GAT训练损失')
        plt.title('MLP vs GAT 训练损失对比')
        plt.xlabel('轮次')
        plt.ylabel('损失')
        plt.legend()
        plt.grid(True)

        # 绘制验证损失
        plt.subplot(2, 1, 2)
        plt.plot(
            epochs_mlp, self.mlp_val_losses[:max_epoch], 'b-', label='MLP验证损失')
        plt.plot(
            epochs_gat, self.gat_val_losses[:max_epoch], 'r-', label='GAT验证损失')
        plt.title('MLP vs GAT 验证损失对比')
        plt.xlabel('轮次')
        plt.ylabel('损失')
        plt.legend()
        plt.grid(True)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path)
        else:
            plt.show()

    def evaluate_mlp_on_new_data(self, test_features=None, test_labels=None):
        """评估MLP模型在新数据上的性能"""
        # 调用models包中的evaluate_mlp_on_new_data函数
        return evaluate_mlp_on_new_data(self, test_features, test_labels)

    def landmarc_localization(self, test_features, test_labels=None, k=None):
        """
        使用LANDMARC算法进行定位

        参数:
            test_features: 测试标签的特征（RSSI和相位值）
            test_labels: 测试标签的真实位置（可选，用于评估）
            k: k近邻数量

        返回:
            预测位置和平均误差（如果提供真实位置）
        """
        if k is None:
            k = self.config['GRID_SEARCH_PARAMS']['k_range'][0]

        # 提取参考标签的特征和位置
        reference_features = self.features
        reference_locations = self.labels

        # 调用导入的landmarc_localization函数
        predictions, avg_error = landmarc_localization(
            reference_features, reference_locations, test_features, test_labels, k
        )

        # 如果启用了预测日志，打印评估信息
        if test_labels is not None and self.config['PREDICTION_LOG']:
            if isinstance(test_labels, torch.Tensor):
                test_labels_np = test_labels.cpu().numpy()
            else:
                test_labels_np = test_labels

            if isinstance(predictions, torch.Tensor):
                predictions_np = predictions.cpu().numpy()
            else:
                predictions_np = predictions

            distances = np.sqrt(
                np.sum((predictions_np - test_labels_np)**2, axis=1))

            log_message = "\nLANDMARC算法位置预测评估:\n"
            log_message += f"测试样本数量: {len(test_features)}\n"
            log_message += f"平均预测误差: {avg_error:.2f}米\n"
            log_message += f"最大误差: {np.max(distances):.2f}米\n"
            log_message += f"最小误差: {np.min(distances):.2f}米\n"
            log_message += f"误差标准差: {np.std(distances):.2f}米\n"

            # 计算不同误差阈值下的准确率
            for threshold in [0.5, 1.0, 1.5, 2.0]:
                accuracy = np.mean(distances < threshold) * 100
                log_message += f"误差 < {threshold}米的准确率: {accuracy:.2f}%\n"

            # 打印到控制台并写入日志
            self.prediction_logger.info(log_message)

        return predictions, avg_error

    def train_hetero_model(
        self, hidden_channels=64, heads=3, lr=0.005, weight_decay=5e-4
    ):
        """训练异构图模型，支持超参数传递，返回验证集损失、平均误差和最佳模型参数"""
        # 创建训练、验证和测试掩码
        train_mask, val_mask, test_mask = create_data_masks(
            len(self.features_norm), self.config, self.device
        )

        # 创建和训练异构图模型
        results = train_hetero_model_func(
            self.features_norm,
            self.labels_norm,
            self.antenna_locations,
            train_mask,
            val_mask,
            test_mask,
            self.labels_scaler,
            self.device,
            {'RANDOM_SEED': self.config['RANDOM_SEED'],
                'EPOCHS': self.config['EPOCHS'],
                'PATIENCE': self.config['PATIENCE'],
                'TRAIN_LOG': self.config['TRAIN_LOG'],
                'RFID_INSTANCE': self,
                'K': self.config['K'],
                'CHAIR_INFO': self.config['CHAIR_INFO'],
                'EDGE_ATTR_WEIGHTS': self.config['EDGE_ATTR_WEIGHTS'],
                'HETERO_PERF_MONITOR': self.config.get('HETERO_PERF_MONITOR', False),
                # 添加MODEL_PARAMS配置
                'MODEL_PARAMS': self.config['MODEL_PARAMS'],
                'USE_TORCH_COMPILE': self.use_torch_compile,  # 传递编译标志
                'RFID_INSTANCE': self  # 将RFIDLocalization实例传递给CONFIG
             },
            hidden_channels=hidden_channels,
            heads=heads,
            lr=lr,
            weight_decay=weight_decay,
            chair_info=self.config['CHAIR_INFO'],
            edge_attr_weights=self.config['EDGE_ATTR_WEIGHTS']
        )

        # 检查返回结果并保存模型
        if isinstance(results, tuple):
            # 期望返回5个值: best_val_avg_distance, best_val_loss, hetero_model, train_losses, val_losses
            if len(results) == 5:
                best_val_avg_distance, best_val_loss, best_model, train_losses, val_losses = results
                # 确保保存模型，即使模型为None也执行赋值
                self.hetero_model = best_model
                # 保存损失历史
                self.hetero_train_losses = train_losses
                self.hetero_val_losses = val_losses
                return best_val_loss, best_val_avg_distance, best_model
            # 处理只返回3个值的情况（向后兼容）
            elif len(results) >= 3:
                best_val_avg_distance, best_val_loss, best_model = results[:3]
                self.hetero_model = best_model
                return best_val_loss, best_val_avg_distance, best_model
            else:
                best_val_avg_distance, best_val_loss = results[:2]
                # 如果没有返回模型，但需要确保有一个模型实例
                if self.hetero_model is None:
                    print(
                        "警告：train_hetero_model_func没有返回模型。创建一个新的HeterogeneousGNNModel实例。")
                    # 创建一个新模型作为备选
                    from models.heterogeneous.model import HeterogeneousGNNModel
                    self.hetero_model = HeterogeneousGNNModel(
                        in_channels=self.features_norm.shape[1],
                        hidden_channels=hidden_channels,
                        out_channels=2,
                        heads=heads
                    ).to(self.device)
                return best_val_loss, best_val_avg_distance, None
        else:
            # 如果只返回一个值（可能是一个误差数值）
            best_val_avg_distance = results
            return best_val_avg_distance, best_val_avg_distance, None

    def evaluate_prediction_hetero_accuracy(self, test_data=None, num_samples=50):
        """
        评估异构图模型在新标签预测上的准确性，并与MLP模型集成以提高精度

        参数:
            test_data: 测试数据，格式为(特征，标签)的元组，如果为None则从文件加载
            num_samples: 如果没有提供测试数据，随机抽样的样本数量

        返回:
            predicted_positions_orig: 预测位置
            avg_distance: 平均误差距离（如果提供了真实标签）
            distances: 所有样本的误差列表（如果提供了真实标签）
        """
        # 检查异构图模型是否为None，如果是，尝试先训练一个
        if self.hetero_model is None:
            print("警告: 异构图模型为None，尝试使用MODEL_PARAMS.异构图参数训练一个模型")
            try:
                # 使用配置中的默认参数训练异构图模型
                hetero_params = self.config['MODEL_PARAMS']['异构图']
                best_val_loss, best_val_avg_distance, _ = self.train_hetero_model(
                    hidden_channels=hetero_params['hidden_channels'],
                    heads=hetero_params['heads'],
                    lr=hetero_params['lr'],
                    weight_decay=hetero_params['weight_decay']
                )
                # 再次检查模型是否已正确初始化
                if self.hetero_model is None:
                    raise ValueError("异构图模型训练失败，请检查训练过程或数据。")
            except Exception as e:
                raise ValueError(
                    f"无法初始化异构图模型：{str(e)}。请先调用train_hetero_model或检查模型训练过程。")

        # 如果是PyTorch 2.0+，尝试编译模型以加速推理
        if self.use_torch_compile and hasattr(self.hetero_model, 'compile'):
            try:
                print("正在编译异构图模型以加速推理...")
                self.hetero_model = self.hetero_model.compile()
                print("模型编译成功")
            except Exception as e:
                print(f"模型编译失败: {e}")

        # 设置随机种子确保可重复性
        np.random.seed(self.config['RANDOM_SEED'])
        torch.manual_seed(self.config['RANDOM_SEED'])
        if torch.cuda.is_available():
            torch.cuda.manual_seed(self.config['RANDOM_SEED'])
            torch.cuda.manual_seed_all(self.config['RANDOM_SEED'])
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False

        # 初始化评估结果
        avg_distance = None
        distances = None
        test_labels = None

        # 如果没有提供测试数据，从文件加载
        if test_data is None:
            test_features, test_labels, test_features_np, test_labels_np, _ = load_and_preprocess_test_data(
                self.config['TEST_DATA_PATH']
            )

            # 将测试特征转移到正确的设备
            test_features = to_device(test_features, self.device)

            # 如果指定了样本数量，则取前num_samples个样本
            if num_samples is not None and num_samples < len(test_features):
                test_features = test_features[:num_samples]
                test_features_np = test_features_np[:num_samples]
                # 只在评估阶段使用test_labels
                if test_labels is not None:
                    test_labels = test_labels[:num_samples]
                    test_labels_np = test_labels_np[:num_samples]
                    test_labels = to_device(test_labels, self.device)
        else:
            test_features_np, test_labels_np = test_data
            # 将测试特征转换为张量
            test_features = to_device(
                torch.tensor(test_features_np,
                             dtype=torch.float32), self.device
            )
            # 只在评估阶段使用test_labels
            if test_labels_np is not None:
                test_labels = to_device(
                    torch.tensor(test_labels_np,
                                 dtype=torch.float32), self.device
                )

        # 标准化RSSI特征 (仅使用RSSI特征)
        rssi_norm = self.scaler_rssi.transform(
            test_features[:, :4].cpu().numpy())
        features_norm = to_device(
            torch.tensor(rssi_norm, dtype=torch.float32),
            self.device
        )

        # 准备天线位置
        antenna_positions = self.antenna_locations.clone()

        # 如果MLP模型为None，先训练一个MLP模型
        if self.mlp_model is None:
            print("异构图评估时发现MLP模型为None，使用MODEL_PARAMS.MLP参数训练MLP模型")
            mlp_params = self.config['MODEL_PARAMS']['MLP']
            mlp_val_loss, mlp_val_avg_distance, _ = self.train_mlp_model(
                hidden_channels=mlp_params['hidden_channels'],
                dropout=mlp_params['dropout'],
                lr=mlp_params['lr'],
                weight_decay=mlp_params['weight_decay']
            )

        # 使用MLP模型进行预测
        self.mlp_model.eval()
        with torch.no_grad():
            mlp_predictions = self.mlp_model(features_norm)

        # 反标准化MLP预测结果
        mlp_predictions_orig = self.labels_scaler.inverse_transform(
            mlp_predictions.cpu().numpy()
        )
        mlp_predictions_orig = to_device(
            torch.tensor(mlp_predictions_orig,
                         dtype=torch.float32), self.device
        )

        # 存储异构图模型预测结果
        hetero_predictions = []

        # 单独预测每个测试样本
        for i in range(len(features_norm)):
            # 提取单个样本特征
            sample_features = features_norm[i:i + 1]

            # 使用MLP预测作为初始位置估计
            temp_labels = mlp_predictions[i:i + 1]

            # 使用add_new_node_to_hetero_graph添加新节点
            # 首先创建初始异构图
            # 使用配置中的椅子信息列表
            chair_info = self.config['CHAIR_INFO']

            # 添加边属性权重配置
            edge_attr_weights = self.config['EDGE_ATTR_WEIGHTS']

            full_hetero_data = create_heterogeneous_graph_data(
                self.features_norm,
                self.labels_norm,
                antenna_positions,
                k=self.config['K'],
                device=self.device,
                chair_info=chair_info,
                edge_attr_weights=edge_attr_weights,
                labels_scaler=self.labels_scaler
            )

            # 添加新节点
            new_hetero_data = add_new_node_to_hetero_graph(
                full_hetero_data,
                sample_features,
                temp_labels,
                k=self.config['K'],
                device=self.device,
                edge_attr_weights=edge_attr_weights,
                labels_scaler=self.labels_scaler
            )

            # 准备edge_index和edge_attr字典
            edge_index_dict = {
                ('tag', 'to', 'tag'): new_hetero_data['tag', 'to', 'tag'].edge_index,
                ('tag', 'to', 'antenna'):
                    new_hetero_data['tag', 'to', 'antenna'].edge_index,
                ('antenna', 'to', 'tag'):
                    new_hetero_data['antenna', 'to', 'tag'].edge_index
            }

            edge_attr_dict = {
                ('tag', 'to', 'tag'): new_hetero_data['tag', 'to', 'tag'].edge_attr,
                ('tag', 'to', 'antenna'):
                    new_hetero_data['tag', 'to', 'antenna'].edge_attr,
                ('antenna', 'to', 'tag'):
                    new_hetero_data['antenna', 'to', 'tag'].edge_attr
            }

            # 如果图中存在椅子相关的边，将其添加到字典中
            if 'chair' in new_hetero_data:
                # 椅子-标签边
                if ('tag', 'to', 'chair') in new_hetero_data:
                    edge_index_dict[('tag', 'to', 'chair')
                                    ] = new_hetero_data['tag', 'to', 'chair'].edge_index
                    edge_attr_dict[('tag', 'to', 'chair')
                                   ] = new_hetero_data['tag', 'to', 'chair'].edge_attr

                if ('chair', 'to', 'tag') in new_hetero_data:
                    edge_index_dict[('chair', 'to', 'tag')
                                    ] = new_hetero_data['chair', 'to', 'tag'].edge_index
                    edge_attr_dict[('chair', 'to', 'tag')
                                   ] = new_hetero_data['chair', 'to', 'tag'].edge_attr

                # 椅子-天线边
                if ('chair', 'to', 'antenna') in new_hetero_data:
                    edge_index_dict[('chair', 'to', 'antenna')
                                    ] = new_hetero_data['chair', 'to',
                                                        'antenna'].edge_index
                    edge_attr_dict[('chair', 'to', 'antenna')
                                   ] = new_hetero_data['chair', 'to',
                                                       'antenna'].edge_attr

                if ('antenna', 'to', 'chair') in new_hetero_data:
                    edge_index_dict[('antenna', 'to', 'chair')
                                    ] = new_hetero_data['antenna', 'to',
                                                        'chair'].edge_index
                    edge_attr_dict[('antenna', 'to', 'chair')
                                   ] = new_hetero_data['antenna', 'to',
                                                       'chair'].edge_attr

                # 椅子遮挡边
                if ('chair', 'obstructs', 'tag') in new_hetero_data:
                    edge_index_dict[('chair', 'obstructs', 'tag')
                                    ] = new_hetero_data['chair', 'obstructs', 'tag'].edge_index
                    edge_attr_dict[('chair', 'obstructs', 'tag')
                                   ] = new_hetero_data['chair', 'obstructs', 'tag'].edge_attr

            # 准备节点特征字典
            x_dict = {
                'tag': new_hetero_data['tag'].x,
                'antenna': new_hetero_data['antenna'].x
            }

            # 如果图中存在椅子节点，添加到特征字典
            if 'chair' in new_hetero_data:
                x_dict['chair'] = new_hetero_data['chair'].x

            # 使用异构图模型进行预测
            self.hetero_model.eval()
            with torch.no_grad():
                # 记录预测开始时间
                pred_start_time = time.time()

                out = self.hetero_model(
                    x_dict, edge_index_dict, edge_attr_dict)

                # 记录预测时间
                record_hetero_prediction_time(pred_start_time)

                # 获取新节点的预测 - 使用tag_mask找到新节点
                new_node_idx = torch.where(new_hetero_data['tag'].tag_mask)[0]
                pred_pos = out[new_node_idx]
                hetero_predictions.append(pred_pos)

        # 堆叠异构图预测结果
        hetero_predictions = torch.cat(hetero_predictions, dim=0)

        # 反标准化异构图预测结果
        hetero_predictions_orig = self.labels_scaler.inverse_transform(
            hetero_predictions.cpu().numpy()
        )
        hetero_predictions_orig = to_device(
            torch.tensor(hetero_predictions_orig,
                         dtype=torch.float32), self.device
        )

        # 集成MLP和异构图模型的预测结果
        # 不使用真实标签来计算误差和调整权重
        # 使用固定权重进行集成，从配置中获取
        hetero_weight = self.config.get('HETERO_ENSEMBLE_WEIGHT', 0.5)

        # 创建权重张量并应用于集成
        ensemble_weights = torch.ones(
            len(hetero_predictions_orig), device=self.device) * hetero_weight
        ensemble_weights = ensemble_weights.unsqueeze(
            1)  # 转换为 [batch_size, 1] 形状

        # 加权融合预测结果
        predicted_positions_orig = ensemble_weights * hetero_predictions_orig + \
            (1 - ensemble_weights) * mlp_predictions_orig

        # === 评估部分（仅当提供真实标签时执行）===
        if test_labels is not None:
            # 确保test_labels和predicted_positions_orig在同一设备上
            test_labels = test_labels.to(self.device)
            predicted_positions_orig = predicted_positions_orig.to(self.device)

            # 计算欧几里得距离误差（仅用于评估）
            distances = torch.sqrt(
                torch.sum((test_labels - predicted_positions_orig)**2, dim=1)
            )
            avg_distance = torch.mean(distances).item()

            # 计算各个模型单独的平均误差，用于日志（仅用于评估）
            # 确保异构图和MLP预测结果也在同一设备上
            hetero_predictions_orig = hetero_predictions_orig.to(self.device)
            mlp_predictions_orig = mlp_predictions_orig.to(self.device)

            hetero_error = torch.sqrt(
                torch.sum((test_labels - hetero_predictions_orig)**2, dim=1))
            mlp_error = torch.sqrt(
                torch.sum((test_labels - mlp_predictions_orig)**2, dim=1))
            hetero_avg_distance = torch.mean(hetero_error).item()
            mlp_avg_distance = torch.mean(mlp_error).item()

            if self.config['PREDICTION_LOG']:
                log_message = "\n异构图模型(集成)新标签位置预测评估:\n"
                log_message += f"测试样本数量: {len(test_features)}\n"
                log_message += f"异构图平均误差: {hetero_avg_distance:.2f}米\n"
                log_message += f"MLP平均误差: {mlp_avg_distance:.2f}米\n"
                log_message += f"集成模型平均误差: {avg_distance:.2f}米\n"
                log_message += f"最大误差: {torch.max(distances).item():.2f}米\n"
                log_message += f"最小误差: {torch.min(distances).item():.2f}米\n"
                log_message += f"误差标准差: {torch.std(distances).item():.2f}米\n"

                # 计算不同误差阈值下的准确率
                for threshold in [0.2, 0.5, 1.0, 1.5, 2.0]:
                    accuracy = (distances < threshold).float(
                    ).mean().item() * 100
                    log_message += f"误差 < {threshold}米的准确率: {accuracy:.2f}%\n"

                # 打印到控制台并写入日志
                self.prediction_logger.info(log_message)

            # 转换distances为numpy数组以便返回
            distances = distances.cpu().numpy()

        # 打印异构图性能摘要
        if self.config.get('HETERO_PERF_MONITOR', False):
            from models.heterogeneous.utils import print_hetero_performance_summary
            print_hetero_performance_summary()

        # 返回预测结果和评估结果（如果有）
        if test_labels is not None:
            return predicted_positions_orig, avg_distance, distances
        else:
            return predicted_positions_orig

    def plot_all_models_loss_comparison(self, save_path=None):
        """绘制所有模型的损失对比图"""
        plt.figure(figsize=(12, 8))
        # 绘制GAT模型的损失
        if self.gat_train_losses and self.gat_val_losses:
            plt.subplot(2, 2, 1)
            plt.plot(self.gat_train_losses, label='训练损失')
            plt.plot(self.gat_val_losses, label='验证损失')
            plt.title('GAT模型损失')
            plt.xlabel('Epoch')
            plt.ylabel('损失')
            plt.legend()
            plt.grid(True, alpha=0.3)

        # 绘制MLP模型的损失
        if self.mlp_train_losses and self.mlp_val_losses:
            plt.subplot(2, 2, 2)
            plt.plot(self.mlp_train_losses, label='训练损失')
            plt.plot(self.mlp_val_losses, label='验证损失')
            plt.title('MLP模型损失')
            plt.xlabel('Epoch')
            plt.ylabel('损失')
            plt.legend()
            plt.grid(True, alpha=0.3)

        # 绘制异构图模型的损失
        if self.hetero_train_losses and self.hetero_val_losses:
            plt.subplot(2, 2, 3)
            plt.plot(self.hetero_train_losses, label='训练损失')
            plt.plot(self.hetero_val_losses, label='验证损失')
            plt.title('异构图模型损失')
            plt.xlabel('Epoch')
            plt.ylabel('损失')
            plt.legend()
            plt.grid(True, alpha=0.3)

        plt.tight_layout()
        if save_path:
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        else:
            plt.show()


def main():
    """主函数"""
    start_time = time.time()  # 记录开始时间
    print("初始化...")

    # 创建RFID定位实例
    rfid_localization = RFIDLocalization(CONFIG)

    # 创建结果目录
    os.makedirs(CONFIG['RESULTS_DIR'], exist_ok=True)
    os.makedirs('images', exist_ok=True)

    # 检查是否启用网格搜索
    if CONFIG['GRID_SEARCH']:
        print("启用网格搜索，开始搜索最佳超参数...")
        # 执行网格搜索
        best_models_results, sorted_model_names = run_grid_search(
            CONFIG,
            RFIDLocalization,
            k_range=CONFIG['GRID_SEARCH_PARAMS']['k_range'],
            lr_range=CONFIG['GRID_SEARCH_PARAMS']['lr_range'],
            weight_decay_range=CONFIG['GRID_SEARCH_PARAMS']['weight_decay_range'],
            hidden_channels_range=CONFIG['GRID_SEARCH_PARAMS']['hidden_channels_range'],
            heads_range=CONFIG['GRID_SEARCH_PARAMS']['heads_range'],
            dropout_range=CONFIG['GRID_SEARCH_PARAMS']['dropout_range'],
            quick_search=CONFIG['QUICK_SEARCH']
        )

        # 使用网格搜索找到的最佳参数更新CONFIG
        if 'MLP' in best_models_results:
            best_mlp = best_models_results['MLP']
            CONFIG['MODEL_PARAMS']['MLP'] = {
                'lr': best_mlp['lr'],
                'weight_decay': best_mlp['weight_decay'],
                'hidden_channels': best_mlp['hidden_channels'],
                'dropout': best_mlp['dropout']
            }

        if 'GAT' in best_models_results:
            best_gat = best_models_results['GAT']
            CONFIG['MODEL_PARAMS']['GAT'] = {
                'K': best_gat['K'],
                'lr': best_gat['lr'],
                'weight_decay': best_gat['weight_decay'],
                'hidden_channels': best_gat['hidden_channels'],
                'heads': best_gat['heads']
            }

        if '异构图' in best_models_results:
            best_hetero = best_models_results['异构图']
            CONFIG['MODEL_PARAMS']['异构图'] = {
                'K': best_hetero['K'],
                'lr': best_hetero['lr'],
                'weight_decay': best_hetero['weight_decay'],
                'hidden_channels': best_hetero['hidden_channels'],
                'heads': best_hetero['heads']
            }

        # 使用网格搜索后的最佳K值更新全局K值
        if 'GAT' in best_models_results:
            CONFIG['K'] = best_models_results['GAT']['K']
        elif '异构图' in best_models_results:
            CONFIG['K'] = best_models_results['异构图']['K']

        # 重新初始化RFID定位实例，使用更新后的配置
        rfid_localization = RFIDLocalization(CONFIG)

    # 设置结果字典
    results = {}

    # MLP定位
    if CONFIG['OPEN_MLP']:
        # 从配置获取MLP参数
        mlp_params = CONFIG['MODEL_PARAMS']['MLP']
        # 训练MLP模型
        mlp_val_loss, mlp_val_avg_distance, _ = rfid_localization.train_mlp_model(
            hidden_channels=mlp_params['hidden_channels'],
            dropout=mlp_params['dropout'],
            lr=mlp_params['lr'],
            weight_decay=mlp_params['weight_decay']
        )
        # 在测试集上评估MLP模型
        mlp_predictions, mlp_targets, mlp_test_avg_distance, mlp_errors = rfid_localization.evaluate_mlp_on_new_data()
        results['MLP'] = {'平均误差(米)': mlp_test_avg_distance, '误差列表': mlp_errors}
        print(
            f"MLP验证集损失: {mlp_val_loss:.6f}, 平均误差: {mlp_val_avg_distance:.4f}米, 测试集平均误差: {mlp_test_avg_distance:.4f}米")

        # 保存MLP误差分布图
        plt.figure(figsize=(10, 6))
        plt.hist(mlp_errors, bins=20, alpha=0.7,
                 color='blue', edgecolor='black')
        plt.axvline(np.mean(mlp_errors), color='red', linestyle='dashed', linewidth=2,
                    label=f'平均误差: {np.mean(mlp_errors):.4f}米')
        plt.axvline(np.median(mlp_errors), color='green', linestyle='dashed', linewidth=2,
                    label=f'中位数误差: {np.median(mlp_errors):.4f}米')
        plt.xlabel('误差 (米)')
        plt.ylabel('频率')
        plt.title('MLP定位误差分布')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('images/mlp_error_distribution.png',
                    dpi=300, bbox_inches='tight')
        plt.close()

    # GAT定位
    if CONFIG['OPEN_GAT']:
        # 从配置获取GAT参数
        gat_params = CONFIG['MODEL_PARAMS']['GAT']
        # 训练GAT模型
        gat_val_loss, gat_val_avg_distance, _ = rfid_localization.train_gat_model(
            hidden_channels=gat_params['hidden_channels'],
            heads=gat_params['heads'],
            lr=gat_params['lr'],
            weight_decay=gat_params['weight_decay']
        )
        # 在测试集上评估GAT模型
        gat_predictions, gat_targets, gat_test_avg_distance, gat_errors = rfid_localization.evaluate_prediction_GAT_accuracy()
        results['GAT'] = {'平均误差(米)': gat_test_avg_distance, '误差列表': gat_errors}
        print(
            f"GAT验证集损失: {gat_val_loss:.6f}, 平均误差: {gat_val_avg_distance:.4f}米, 测试集平均误差: {gat_test_avg_distance:.4f}米")

        # 保存GAT误差分布图
        plt.figure(figsize=(10, 6))
        plt.hist(gat_errors, bins=20, alpha=0.7,
                 color='blue', edgecolor='black')
        plt.axvline(np.mean(gat_errors), color='red', linestyle='dashed', linewidth=2,
                    label=f'平均误差: {np.mean(gat_errors):.4f}米')
        plt.axvline(np.median(gat_errors), color='green', linestyle='dashed', linewidth=2,
                    label=f'中位数误差: {np.median(gat_errors):.4f}米')
        plt.xlabel('误差 (米)')
        plt.ylabel('频率')
        plt.title('GAT定位误差分布')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('images/gat_error_distribution.png',
                    dpi=300, bbox_inches='tight')
        plt.close()

    # LANDMARC定位
    if CONFIG['OPEN_LANDMARC']:
        print("开始LANDMARC定位...")
        # 加载测试数据
        test_features, test_labels, test_features_np, test_labels_np, _ = load_and_preprocess_test_data(
            CONFIG['TEST_DATA_PATH']
        )
        # 执行LANDMARC定位
        landmarc_predictions, landmarc_test_avg_distance = rfid_localization.landmarc_localization(
            test_features, test_labels, k=CONFIG['GRID_SEARCH_PARAMS']['k_range'][0]
        )
        # 计算误差
        landmarc_errors = np.sqrt(
            np.sum((landmarc_predictions - test_labels_np)**2, axis=1))
        results['LANDMARC'] = {
            '平均误差(米)': landmarc_test_avg_distance, '误差列表': landmarc_errors}
        print(f"LANDMARC测试集平均误差: {landmarc_test_avg_distance:.4f}米")

        # 保存LANDMARC误差分布图
        plt.figure(figsize=(10, 6))
        plt.hist(landmarc_errors, bins=20, alpha=0.7,
                 color='blue', edgecolor='black')
        plt.axvline(np.mean(landmarc_errors), color='red', linestyle='dashed', linewidth=2,
                    label=f'平均误差: {np.mean(landmarc_errors):.4f}米')
        plt.axvline(np.median(landmarc_errors), color='green', linestyle='dashed', linewidth=2,
                    label=f'中位数误差: {np.median(landmarc_errors):.4f}米')
        plt.xlabel('误差 (米)')
        plt.ylabel('频率')
        plt.title('LANDMARC定位误差分布')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('images/landmarc_error_distribution.png',
                    dpi=300, bbox_inches='tight')
        plt.close()

    # 异构图模型定位
    if CONFIG['OPEN_HETERO']:
        print("开始异构图定位...")
        # 从配置获取异构图参数
        hetero_params = CONFIG['MODEL_PARAMS']['异构图']
        # 训练异构图模型
        hetero_val_loss, hetero_val_avg_distance, _ = rfid_localization.train_hetero_model(
            hidden_channels=hetero_params['hidden_channels'],
            heads=hetero_params['heads'],
            lr=hetero_params['lr'],
            weight_decay=hetero_params['weight_decay']
        )

        # 正确加载测试数据
        test_features, test_labels, test_features_np, test_labels_np, _ = load_and_preprocess_test_data(
            CONFIG['TEST_DATA_PATH']
        )

        # 在测试集上评估异构图模型（显式传递测试数据）
        hetero_results = rfid_localization.evaluate_prediction_hetero_accuracy(
            test_data=(test_features_np, test_labels_np),
            num_samples=len(test_features_np)  # 使用所有测试样本
        )

        # 根据返回值数量确定是否包含评估结果
        if len(hetero_results) == 3:
            hetero_predictions, hetero_test_avg_distance, hetero_errors = hetero_results
        else:
            # 如果只返回预测结果，没有评估结果
            hetero_predictions = hetero_results
            hetero_test_avg_distance = float('nan')
            hetero_errors = []

        results['异构图'] = {
            '平均误差(米)': hetero_test_avg_distance, '误差列表': hetero_errors}
        print(
            f"异构图验证集损失: {hetero_val_loss:.6f}, 平均误差: {hetero_val_avg_distance:.4f}米, 测试集平均误差: {hetero_test_avg_distance:.4f}米")

        # 保存异构图误差分布图
        plt.figure(figsize=(10, 6))
        plt.hist(hetero_errors, bins=20, alpha=0.7,
                 color='blue', edgecolor='black')
        plt.axvline(np.mean(hetero_errors), color='red', linestyle='dashed', linewidth=2,
                    label=f'平均误差: {np.mean(hetero_errors):.4f}米')
        plt.axvline(np.median(hetero_errors), color='green', linestyle='dashed', linewidth=2,
                    label=f'中位数误差: {np.median(hetero_errors):.4f}米')
        plt.xlabel('误差 (米)')
        plt.ylabel('频率')
        plt.title('异构图定位误差分布')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.savefig('images/hetero_error_distribution.png',
                    dpi=300, bbox_inches='tight')
        plt.close()

    # 绘制所有模型的损失对比图
    rfid_localization.plot_all_models_loss_comparison(
        save_path=CONFIG['MODEL_COMPARISON_IMAGE']
    )

    # 输出各模型误差对比
    print("\n各模型误差对比:")
    for model_name, model_result in results.items():
        if '平均误差(米)' in model_result:
            print(f"{model_name}: {model_result['平均误差(米)']:.4f}米")

    # 找出最佳模型
    best_model = min(results.items(), key=lambda x: x[1].get(
        '平均误差(米)', float('inf')))
    print(f"\n最佳模型: {best_model[0]}, 平均误差: {best_model[1]['平均误差(米)']:.4f}米")


if __name__ == "__main__":
    main()
