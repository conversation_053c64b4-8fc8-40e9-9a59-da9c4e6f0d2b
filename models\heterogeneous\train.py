# -*- coding: utf-8 -*-
"""
异构图神经网络模型的训练函数
"""

import torch
import numpy as np
import time
from models.heterogeneous.model import HeterogeneousGNNModel
from models.heterogeneous.utils import create_heterogeneous_graph_data
from models.heterogeneous.utils import start_hetero_model_timer, stop_hetero_model_timer, start_hetero_training_timer, stop_hetero_training_timer, print_hetero_performance_summary
from sklearn.preprocessing import MinMaxScaler
import torch.nn.functional as F


def train_hetero_model(
    features_norm,
    labels_norm,
    antenna_locations_norm,
    train_mask,
    val_mask,
    test_mask,
    labels_scaler,
    device,
    config,
    hidden_channels=64,
    heads=3,
    lr=0.005,
    weight_decay=5e-4,
    chair_info=None,
    edge_attr_weights=None
):
    """
    训练异构图神经网络模型

    参数:
        features_norm: 标准化的特征（仅包含RSSI特征）
        labels_norm: 标准化的标签
        antenna_locations_norm: 标准化的天线位置
        train_mask: 训练掩码
        val_mask: 验证掩码
        test_mask: 测试掩码
        labels_scaler: 标签缩放器，用于反向转换预测结果
        device: 计算设备
        config: 配置字典
        hidden_channels: 隐藏层神经元数量
        heads: 注意力头数量
        lr: 学习率
        weight_decay: 权重衰减
        chair_info: 椅子信息字典，如果不为None则添加椅子节点
        edge_attr_weights: 边属性计算的权重参数字典

    返回:
        元组 (best_val_avg_distance, best_val_loss, model, train_losses, val_losses)
    """
    # 启动异构图模型总计时器
    start_hetero_model_timer()

    # 性能监控字典
    perf_stats = {
        'data_prep': 0,
        'graph_creation': 0,
        'model_forward': 0,
        'loss_calc': 0,
        'backward': 0,
        'validation': 0,
        'epoch_times': []
    }

    # 确保每次运行结果的可重现性
    random_seed = config['RANDOM_SEED']
    torch.manual_seed(random_seed)
    np.random.seed(random_seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(random_seed)
        torch.cuda.manual_seed_all(random_seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

    # 检查模型的数据类型和设备
    start_time = time.time()
    data_prep_start = time.time()
    if hasattr(features_norm, 'device'):
        print(f"特征设备: {features_norm.device}")

    # 数据预处理
    graph_data_start = time.time()
    k_value = config['MODEL_PARAMS']['异构图']['K']

    # 预先将数据全部移至目标设备，减少后续数据迁移开销
    if features_norm.device != device:
        features_norm = features_norm.to(device, non_blocking=True)
    if labels_norm.device != device:
        labels_norm = labels_norm.to(device, non_blocking=True)
    if antenna_locations_norm.device != device:
        antenna_locations_norm = antenna_locations_norm.to(
            device, non_blocking=True)

    # 创建异构图数据
    hetero_data = create_heterogeneous_graph_data(
        features_norm,
        labels_norm,
        antenna_locations_norm,
        k=k_value,
        device=device,
        chair_info=chair_info,
        edge_attr_weights=edge_attr_weights,
        hetero_perf_monitor=config.get('HETERO_PERF_MONITOR', True),
        labels_scaler=labels_scaler
    )
    perf_stats['graph_creation'] = time.time() - graph_data_start

    perf_stats['data_prep'] = time.time() - data_prep_start

    # 创建模型实例
    hetero_model = HeterogeneousGNNModel(
        in_channels=features_norm.shape[1],
        hidden_channels=hidden_channels,
        out_channels=2,
        heads=heads
    ).to(device)

    # 检查是否启用模型编译
    use_torch_compile = config.get('USE_TORCH_COMPILE', False)
    if use_torch_compile:
        try:
            print("正在编译异构图模型以加速训练...")
            if hasattr(hetero_model, 'compile'):
                hetero_model = hetero_model.compile()
                print("模型编译成功！")
            else:
                print("模型没有compile方法，跳过编译")
        except Exception as e:
            print(f"模型编译失败: {e}")
            print("继续使用未编译的模型")

    # 添加训练和验证掩码到异构数据
    hetero_data['tag'].train_mask = train_mask.to(
        device) | test_mask.to(device)
    hetero_data['tag'].val_mask = val_mask.to(device)

    # 创建优化器
    torch.manual_seed(random_seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(random_seed)
        torch.cuda.manual_seed_all(random_seed)
    optimizer = torch.optim.AdamW(
        hetero_model.parameters(), lr=lr, weight_decay=weight_decay, betas=(0.9, 0.999)
    )

    # 定义带权重的MSE损失函数
    def weighted_mse_loss(pred, target, weights=None):
        """
        带权重的MSE损失函数

        参数:
            pred: 预测值 [batch_size, 2]
            target: 目标值 [batch_size, 2]
            weights: 样本权重 [batch_size]

        返回:
            加权平均的MSE损失
        """
        if weights is None:
            return F.mse_loss(pred, target)

        # 计算每个样本的MSE
        mse_per_sample = torch.sum((pred - target) ** 2, dim=1)
        # 加权平均
        return torch.mean(mse_per_sample * weights)

    # 定义焦点损失函数 - 对难样本给予更高权重
    def focal_mse_loss(pred, target, gamma=2.0):
        """
        焦点MSE损失，对难样本给予更高权重

        参数:
            pred: 预测值 [batch_size, 2]
            target: 目标值 [batch_size, 2]
            gamma: 焦点损失的指数因子，越大对难样本权重越高

        返回:
            焦点MSE损失
        """
        # 计算每个样本的MSE
        mse_per_sample = torch.sum((pred - target) ** 2, dim=1)
        # 归一化MSE到[0,1]范围
        max_mse = torch.max(mse_per_sample).detach()
        if max_mse > 0:
            normalized_mse = mse_per_sample / max_mse
        else:
            normalized_mse = mse_per_sample

        # 计算焦点权重 - 误差越大，权重越高
        focal_weights = (normalized_mse + 0.1) ** gamma

        # 应用焦点权重
        weighted_mse = mse_per_sample * focal_weights
        return torch.mean(weighted_mse)

    # 使用组合损失函数
    def combined_loss(pred, target, alpha=0.7, gamma=2.0):
        """
        组合MSE和焦点损失

        参数:
            pred: 预测值
            target: 目标值
            alpha: MSE损失的权重
            gamma: 焦点损失的指数因子

        返回:
            组合损失
        """
        mse_loss = F.mse_loss(pred, target)
        focal_loss = focal_mse_loss(pred, target, gamma)
        return alpha * mse_loss + (1 - alpha) * focal_loss

    # 使用自适应损失函数
    def adaptive_loss(pred, target, epoch, max_epochs, gamma=2.0):
        """
        自适应损失函数，随训练进度动态调整损失组合

        参数:
            pred: 预测值
            target: 目标值
            epoch: 当前轮次
            max_epochs: 最大轮次
            gamma: 焦点损失的指数因子

        返回:
            自适应组合损失
        """
        # 随着训练进行，逐渐增加焦点损失的比重
        progress = min(1.0, epoch / (max_epochs * 0.7))  # 在70%的训练过程中完成过渡
        alpha = max(0.3, 0.8 - 0.5 * progress)  # alpha从0.8逐渐降至0.3

        # 计算欧氏距离
        distances = torch.sqrt(torch.sum((pred - target) ** 2, dim=1))

        # 对距离较大的样本增加权重
        weights = (distances + 0.1) ** gamma
        weights = weights / weights.sum() * len(weights)  # 归一化但保持总和不变

        # 计算加权MSE
        mse_per_sample = torch.sum((pred - target) ** 2, dim=1)
        weighted_mse = torch.mean(mse_per_sample * weights)

        # 计算普通MSE
        mse_loss = F.mse_loss(pred, target)

        # 组合损失
        return alpha * mse_loss + (1 - alpha) * weighted_mse

    # 1.6 添加多尺度注意力调整函数
    def adjust_multiscale_params(model, epoch, max_epochs):
        """
        动态调整多尺度注意力参数

        参数:
            model: 模型实例
            epoch: 当前轮次
            max_epochs: 最大轮次
        """
        if hasattr(model, 'use_multiscale_attention') and model.use_multiscale_attention:
            # 在训练过程中逐渐增加多尺度特征的权重
            progress = min(1.0, epoch / (max_epochs * 0.6))  # 在60%的训练过程中完成过渡
            # 多尺度特征融合权重从0.2增加到0.4
            model.multiscale_alpha = 0.2 + 0.2 * progress

            # 打印当前多尺度参数（可选，用于调试）
            if epoch % 100 == 0:
                print(f"多尺度注意力参数 - alpha: {model.multiscale_alpha:.4f}")

    # 创建余弦衰减学习率调度器，带预热阶段
    warmup_epochs = 80  # 进一步增加预热阶段的轮次数
    max_epochs = config.get('EPOCHS', 1500)  # 增加最大训练轮次

    # 学习率预热函数
    def warmup_lr_scheduler(epoch):
        if epoch < warmup_epochs:
            # 线性预热
            return epoch / warmup_epochs
        else:
            # 余弦衰减
            decay_epochs = max_epochs - warmup_epochs
            epoch_adj = epoch - warmup_epochs
            cosine_decay = 0.5 * (1 + np.cos(np.pi * epoch_adj / decay_epochs))
            return max(0.03, cosine_decay)  # 最小学习率为初始学习率的3%

    # 创建学习率调度器
    scheduler = torch.optim.lr_scheduler.LambdaLR(
        optimizer, lr_lambda=warmup_lr_scheduler)

    # 为MinMaxScaler参数创建张量
    data_min = torch.as_tensor(
        labels_scaler.data_min_, dtype=torch.float32).to(device)
    data_range = torch.as_tensor(labels_scaler.data_range_,
                                 dtype=torch.float32).to(device)

    # 开始训练
    best_val_loss = float('inf')
    best_model = None
    patience = config.get('PATIENCE', 100)  # 增加早停耐心值
    counter = 0  # 计数器
    best_val_avg_distance = float('inf')

    # 使用计数器记录连续验证集指标没有改善的次数
    no_improve_counter = 0
    min_epochs = 400  # 增加最小训练轮次
    best_epoch = 0  # 记录最佳模型的轮次

    # 初始化损失记录
    train_losses = []
    val_losses = []

    # 记录移动平均验证误差 - 用于更稳定的早停判断
    val_distance_history = []
    moving_avg_window = 5  # 移动平均窗口大小

    # 准备edge_index和edge_attr字典，确保所有数据在GPU上
    # 优化：预先将所有边数据批量移至设备，减少散布式传输开销
    edge_index_dict = {}
    edge_attr_dict = {}

    for edge_type in hetero_data.edge_types:
        if hasattr(hetero_data[edge_type], 'edge_index'):
            edge_index_dict[edge_type] = hetero_data[edge_type].edge_index.to(
                device)
            if hasattr(hetero_data[edge_type], 'edge_attr'):
                edge_attr_dict[edge_type] = hetero_data[edge_type].edge_attr.to(
                    device)

    # 准备节点特征字典，确保所有数据在GPU上
    # 优化：使用批量数据迁移
    x_dict = {}
    for node_type in hetero_data.node_types:
        if hasattr(hetero_data[node_type], 'x'):
            x_dict[node_type] = hetero_data[node_type].x.to(device)

    # 训练掩码和标签预先获取避免重复索引
    train_mask_tensor = hetero_data['tag'].train_mask
    val_mask_tensor = hetero_data['tag'].val_mask
    train_target = hetero_data['tag'].pos[train_mask_tensor]

    # 训练循环
    for epoch in range(config.get('EPOCHS', 1000)):
        epoch_start = time.time()

        # 训练阶段
        hetero_model.train()
        optimizer.zero_grad()

        # 开始训练计时
        start_hetero_training_timer()

        # 1.6 优化：调整多尺度注意力参数
        adjust_multiscale_params(hetero_model, epoch, max_epochs)

        # 前向传播
        forward_start = time.time()
        out = hetero_model(x_dict, edge_index_dict, edge_attr_dict)
        torch.cuda.synchronize(device) if device.type == 'cuda' else None
        perf_stats['model_forward'] += time.time() - forward_start

        # 计算训练损失 - 使用自适应损失函数
        loss_start = time.time()
        train_pred = out[train_mask_tensor]

        # 使用自适应损失函数
        train_loss = adaptive_loss(
            train_pred, train_target, epoch, max_epochs, gamma=2.0)

        # 1.4 优化：添加注意力正则化损失
        if hasattr(hetero_model, 'attention_l2_loss') and hasattr(hetero_model, 'attention_entropy_loss'):
            # 获取注意力正则化损失
            attention_l2_loss = getattr(hetero_model, 'attention_l2_loss', 0)
            attention_entropy_loss = getattr(
                hetero_model, 'attention_entropy_loss', 0)

            # 动态调整正则化权重 - 随着训练进行，逐渐增加正则化强度
            l2_weight = min(0.01, 0.001 + epoch * 0.00002)  # 从0.001逐渐增加到0.01
            entropy_weight = min(0.01, 0.001 + epoch *
                                 0.00002)  # 从0.001逐渐增加到0.01

            # 添加到总损失
            regularization_loss = l2_weight * attention_l2_loss + \
                entropy_weight * attention_entropy_loss
            train_loss = train_loss + regularization_loss

        torch.cuda.synchronize(device) if device.type == 'cuda' else None
        perf_stats['loss_calc'] += time.time() - loss_start

        # 反向传播
        backward_start = time.time()
        train_loss.backward()
        torch.cuda.synchronize(device) if device.type == 'cuda' else None
        perf_stats['backward'] += time.time() - backward_start

        # 添加梯度裁剪，防止梯度爆炸
        torch.nn.utils.clip_grad_norm_(hetero_model.parameters(), max_norm=1.0)

        optimizer.step()

        # 停止训练计时
        stop_hetero_training_timer()

        # 验证阶段
        val_start = time.time()
        hetero_model.eval()
        with torch.no_grad():
            # 前向传播
            out = hetero_model(x_dict, edge_index_dict, edge_attr_dict)

            # 计算验证损失 - 使用普通MSE损失
            val_loss = F.mse_loss(
                out[val_mask_tensor],
                hetero_data['tag'].pos[val_mask_tensor]
            )

            # 将预测结果转换回原始比例（逆MinMaxScaler）
            out_orig = out * data_range + data_min
            y_orig = hetero_data['tag'].pos * data_range + data_min

            # 计算训练集和验证集的距离误差
            train_distances = torch.sqrt(
                torch.sum((
                    out_orig[train_mask_tensor] -
                    y_orig[train_mask_tensor]
                )**2,
                    dim=1)
            )
            train_accuracy = (train_distances <
                              0.3).float().mean().item() * 100
            train_avg_distance = train_distances.mean().item()

            val_distances = torch.sqrt(
                torch.sum((
                    out_orig[val_mask_tensor] -
                    y_orig[val_mask_tensor]
                )**2,
                    dim=1)
            )
            val_accuracy = (val_distances < 0.3).float().mean().item() * 100
            val_avg_distance = val_distances.mean().item()

        torch.cuda.synchronize(device) if device.type == 'cuda' else None
        perf_stats['validation'] += time.time() - val_start

        # 更新学习率
        scheduler.step()

        # 保存每个epoch的损失值
        train_losses.append(train_loss.item())
        val_losses.append(val_loss.item())

        # 更新验证误差历史
        val_distance_history.append(val_avg_distance)

        # 计算移动平均验证误差
        if len(val_distance_history) >= moving_avg_window:
            moving_avg_val_distance = np.mean(
                val_distance_history[-moving_avg_window:])
        else:
            moving_avg_val_distance = val_avg_distance

        # 记录每个epoch的总时间
        epoch_time = time.time() - epoch_start
        perf_stats['epoch_times'].append(epoch_time)

        # 早停检查 - 使用移动平均验证误差
        improved = False

        # 修改优化条件，使用移动平均来判断是否有真正的改善
        if epoch >= moving_avg_window:  # 至少训练足够轮次后再开始判断
            # 如果移动平均验证误差比历史最佳小，认为有改善
            if moving_avg_val_distance < best_val_avg_distance * 0.995:  # 至少改善0.5%
                best_val_avg_distance = val_avg_distance
                best_val_loss = val_loss
                best_model = hetero_model.state_dict().copy()
                best_epoch = epoch
                improved = True
                no_improve_counter = 0
            else:
                no_improve_counter += 1
        else:
            # 前几个epoch直接更新最佳模型
            if val_avg_distance < best_val_avg_distance:
                best_val_avg_distance = val_avg_distance
                best_val_loss = val_loss
                best_model = hetero_model.state_dict().copy()
                best_epoch = epoch
                improved = True
                no_improve_counter = 0

        # 只有在超过最小训练轮次后才考虑早停
        if no_improve_counter >= patience and epoch >= min_epochs:
            if config.get('TRAIN_LOG', False):
                # 使用RFIDLocalization实例通过传入的config获取
                rfid_instance = config.get('RFID_INSTANCE', None)
                log_message = (
                    f"异构图模型轮次 {epoch}\n"
                    f"训练集 - 损失: {train_loss.item():.4f}, 准确率: {train_accuracy:.2f}%, 平均误差: {train_avg_distance:.2f}米\n"
                    f"验证集 - 损失: {val_loss.item():.4f}, 准确率: {val_accuracy:.2f}%, 平均误差: {val_avg_distance:.2f}米\n"
                    f"\n触发早停！在轮次 {epoch} 停止训练\n"
                    f"最佳验证损失: {best_val_loss:.4f}, 最佳验证集平均误差: {best_val_avg_distance:.2f}米, 最佳轮次: {best_epoch}"
                )

                if rfid_instance and hasattr(rfid_instance, 'train_logger'):
                    rfid_instance.train_logger.info(log_message)

            # 加载最佳模型
            hetero_model.load_state_dict(best_model)
            break

        if epoch % 50 == 0 and config.get('TRAIN_LOG', False):
            rfid_instance = config.get('RFID_INSTANCE', None)
            current_lr = optimizer.param_groups[0]['lr']
            log_message = (
                f"异构图模型轮次 {epoch}\n"
                f"训练集 - 损失: {train_loss.item():.4f}, 准确率: {train_accuracy:.2f}%, 平均误差: {train_avg_distance:.2f}米\n"
                f"验证集 - 损失: {val_loss.item():.4f}, 准确率: {val_accuracy:.2f}%, 平均误差: {val_avg_distance:.2f}米\n"
                f"当前学习率: {current_lr:.6f}"
            )

            # 1.6 优化：记录多尺度注意力参数
            if hasattr(hetero_model, 'use_multiscale_attention') and hetero_model.use_multiscale_attention:
                log_message += f"\n多尺度注意力 - 融合权重: {hetero_model.multiscale_alpha:.4f}"

            # 1.6 优化：记录注意力机制统计信息
            if hasattr(hetero_model, 'attention_stats'):
                attn_stats = hetero_model.get_performance_stats()
                ms_used = attn_stats.get('multiscale_used', 0)
                ca_used = attn_stats.get('cross_attention_used', 0)
                ms_improvement = attn_stats.get(
                    'multiscale_avg_improvement', 0)
                ca_improvement = attn_stats.get(
                    'cross_attention_avg_improvement', 0)

                log_message += (
                    f"\n注意力统计 - 多尺度使用次数: {ms_used}, 平均改进: {ms_improvement:.4f}"
                    f"\n注意力统计 - 跨类型使用次数: {ca_used}, 平均改进: {ca_improvement:.4f}"
                )

                # 重置统计信息，开始新一轮统计
                if hasattr(hetero_model, 'reset_attention_stats'):
                    hetero_model.reset_attention_stats()

            if rfid_instance and hasattr(rfid_instance, 'train_logger'):
                rfid_instance.train_logger.info(log_message)

            # 每50个epoch记录一次性能统计
            if epoch > 0:
                perf_log = (
                    f"性能统计 - 平均每轮时间: {sum(perf_stats['epoch_times'])/len(perf_stats['epoch_times']):.4f}秒\n"
                    f"数据准备: {perf_stats['data_prep']:.4f}秒, 图创建: {perf_stats['graph_creation']:.4f}秒\n"
                    f"前向传播: {perf_stats['model_forward']/epoch:.4f}秒/轮, 损失计算: {perf_stats['loss_calc']/epoch:.4f}秒/轮\n"
                    f"反向传播: {perf_stats['backward']/epoch:.4f}秒/轮, 验证: {perf_stats['validation']/epoch:.4f}秒/轮"
                )
                if rfid_instance and hasattr(rfid_instance, 'train_logger'):
                    rfid_instance.train_logger.info(perf_log)

    # 确保加载最佳模型
    if best_model is not None:
        hetero_model.load_state_dict(best_model)

    # 训练完成，停止计时
    stop_hetero_model_timer()

    # 返回最佳验证集平均误差、损失、模型和损失历史
    return best_val_avg_distance, best_val_loss, hetero_model, train_losses, val_losses
